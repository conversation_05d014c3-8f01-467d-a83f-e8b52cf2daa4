import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import { toast } from '@/hooks/use-toast';
import type { components } from '@/types/api';

// Query Keys
export const profileQueryKeys = {
  all: ['profile'] as const,
  me: () => [...profileQueryKeys.all, 'me'] as const,
};

// Types
type UserProfile = components['schemas']['UserProfile'];
type UpdateProfileRequest = components['schemas']['UpdateProfileRequest'];
type UpdateProfileResponse = components['schemas']['UpdateProfileResponse'];

// Profile Queries
export function useProfileQuery(enabled = true) {
  return useQuery({
    queryKey: profileQueryKeys.me(),
    queryFn: () => apiClient.getProfile(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 (profile not found) or 401 (unauthorized)
      if (error?.status === 404 || error?.status === 401) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

// Profile Mutations
export function useUpdateProfileMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateProfileRequest) => 
      apiClient.updateProfile(data),
    onSuccess: (data: UpdateProfileResponse) => {
      // Update the profile cache with the response data
      queryClient.setQueryData(profileQueryKeys.me(), data);
      
      // Also invalidate auth session to update user context
      queryClient.invalidateQueries({ queryKey: ['auth', 'session'] });
      
      toast({
        title: 'Success',
        description: 'Profile updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update profile',
        variant: 'destructive',
      });
    },
  });
}

// File Upload for Avatar
export function useAvatarUploadMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      // Upload the file first
      const uploadResponse = await apiClient.uploadFile(file, {
        fileType: 'image',
        entityType: 'profile',
        isPublic: false,
      });

      // Then update the profile with the new avatar file ID
      const profileResponse = await apiClient.updateProfile({
        avatarFileId: uploadResponse.file.id,
      });

      return { uploadResponse, profileResponse };
    },
    onSuccess: ({ profileResponse }) => {
      // Update the profile cache
      queryClient.setQueryData(profileQueryKeys.me(), profileResponse);
      
      // Invalidate auth session to update user context
      queryClient.invalidateQueries({ queryKey: ['auth', 'session'] });
      
      toast({
        title: 'Success',
        description: 'Avatar updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update avatar',
        variant: 'destructive',
      });
    },
  });
}

// Utility hook for profile preferences
export function useUpdateProfilePreferencesMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (preferences: components['schemas']['ProfilePreferences']) => 
      apiClient.updateProfile({ preferences }),
    onSuccess: (data: UpdateProfileResponse) => {
      // Update the profile cache
      queryClient.setQueryData(profileQueryKeys.me(), data);
      
      toast({
        title: 'Success',
        description: 'Preferences updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update preferences',
        variant: 'destructive',
      });
    },
  });
}

// Utility hook for updating specific profile fields
export function useUpdateProfileFieldMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<UpdateProfileRequest>) => 
      apiClient.updateProfile(data),
    onSuccess: (data: UpdateProfileResponse) => {
      // Update the profile cache
      queryClient.setQueryData(profileQueryKeys.me(), data);
      
      toast({
        title: 'Success',
        description: 'Profile updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update profile',
        variant: 'destructive',
      });
    },
  });
}
