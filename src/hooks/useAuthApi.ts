import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, type SessionResponse, type AuthResponse, type SignUpRequest, type SignInRequest, authResponseToSessionResponse } from '@/lib/api-client';
import { toast } from '@/hooks/use-toast';

// Query keys for authentication
export const authQueryKeys = {
  session: () => ['auth', 'session'] as const,
  user: () => ['auth', 'user'] as const,
} as const;

// Types for auth mutations - using API schema types

interface SignOutData extends Record<string, unknown> {
  // Empty for now, but can be extended
}

// Session Query Hook
export function useSessionQuery(enabled = true) {
  return useQuery({
    queryKey: authQueryKeys.session(),
    queryFn: async () => {
      console.log(' [useSessionQuery] Fetching session from server...');
      const result = await apiClient.getSession();
      console.log(' [useSessionQuery] Session fetch result:', {
        hasUser: !!result?.user,
        userEmail: result?.user?.email,
        hasSession: !!result?.session
      });
      return result;
    },
    enabled: enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      console.log(' [useSessionQuery] Retry attempt:', { failureCount, error: error?.status });
      // Don't retry on 401/403 errors (unauthenticated/unauthorized)
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false, // Prevent refetch on window focus during sign out
  });
}

// Sign In Mutation Hook
export function useSignInMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SignInRequest) => {
      return apiClient.signInWithEmail(data);
    },
    onSuccess: (data: AuthResponse) => {
      // Convert AuthResponse to SessionResponse format and set it directly in the cache
      const sessionData = authResponseToSessionResponse(data);
      queryClient.setQueryData(authQueryKeys.session(), sessionData);

      // Invalidate all user-related queries to refresh them with new auth state
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['listings'] });
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });

    },
    onError: (error: any) => {
      console.error('Sign in error:', error);
      
      let errorMessage = 'Sign in failed';
      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.status === 401) {
        errorMessage = 'Invalid email or password';
      } else if (error?.status === 403) {
        errorMessage = 'Account access denied';
      }
      
      toast({
        title: 'Sign In Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });
}

// Sign Up Mutation Hook
export function useSignUpMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SignUpRequest) => {
      return apiClient.signUpWithEmail(data);
    },
    onSuccess: (data: AuthResponse) => {
      // Convert AuthResponse to SessionResponse format and set it directly in the cache
      const sessionData = authResponseToSessionResponse(data);
      queryClient.setQueryData(authQueryKeys.session(), sessionData);

      // Invalidate all user-related queries to refresh them with new auth state
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['listings'] });
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });

      toast({
        title: 'Success',
        description: 'Account created successfully',
      });
    },
    onError: (error: any) => {
      console.error('Sign up error:', error);
      
      let errorMessage = 'Sign up failed';
      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.status === 409) {
        errorMessage = 'Email already exists';
      } else if (error?.status === 400) {
        errorMessage = 'Invalid registration data';
      }
      
      toast({
        title: 'Sign Up Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });
}

// Sign Out Mutation Hook
export function useSignOutMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SignOutData = {}) => {
      return apiClient.signOut(data);
    },
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();
      
      toast({
        title: 'Success',
        description: 'Successfully signed out',
      });
    },
    onError: (error: any) => {
      console.error('Sign out error:', error);
      
      // Even if sign out fails on the server, clear local cache
      queryClient.clear();
      
      toast({
        title: 'Sign Out',
        description: 'Signed out (with warnings)',
        variant: 'default',
      });
    },
  });
}

// Helper hook to get current user from session
export function useCurrentUser() {
  const { data: session, ...rest } = useSessionQuery();
  
  return {
    ...rest,
    data: session?.user || null,
    user: session?.user || null,
  };
}

// Helper hook to check if user is authenticated
export function useIsAuthenticated() {
  const { data: session, isLoading, error } = useSessionQuery();
  
  return {
    isAuthenticated: !!session?.user && !!session?.session,
    isLoading,
    error,
  };
}
