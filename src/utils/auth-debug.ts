/**
 * Authentication debugging utilities
 */

export interface AuthDebugInfo {
  hasAccessToken: boolean;
  accessTokenLength?: number;
  hasRefreshTokenCookie: boolean;
  cookieNames: string[];
  userAgent: string;
  currentUrl: string;
  timestamp: string;
}

/**
 * Collect authentication debugging information
 */
export function getAuthDebugInfo(accessToken?: string | null): AuthDebugInfo {
  const cookies = document.cookie.split(';').map(c => c.trim().split('=')[0]);

  return {
    hasAccessToken: !!accessToken,
    accessTokenLength: accessToken?.length,
    hasRefreshTokenCookie: cookies.some(name =>
      name.includes('refresh') ||
      name.includes('better-auth') ||
      name.includes('session')
    ),
    cookieNames: cookies,
    userAgent: navigator.userAgent,
    currentUrl: window.location.href,
    timestamp: new Date().toISOString()
  };
}

/**
 * Log authentication debugging information
 */
export function logAuthDebug(context: string, accessToken?: string | null, additionalInfo?: any) {
  const debugInfo = getAuthDebugInfo(accessToken);
  
  console.group(`🔐 Auth Debug: ${context}`);
  console.log('Debug Info:', debugInfo);
  if (additionalInfo) {
    console.log('Additional Info:', additionalInfo);
  }
  console.groupEnd();
}

/**
 * Check if the current environment might have CORS issues
 */
export function checkCorsIssues(): { hasCorsIssues: boolean; reason?: string } {
  const currentOrigin = window.location.origin;
  const apiUrl = (import.meta as any).env?.VITE_API_URL || 'http://localhost:3001';
  
  try {
    const apiOrigin = new URL(apiUrl).origin;
    
    if (currentOrigin !== apiOrigin) {
      return {
        hasCorsIssues: true,
        reason: `Frontend (${currentOrigin}) and API (${apiOrigin}) are on different origins`
      };
    }
  } catch (error) {
    return {
      hasCorsIssues: true,
      reason: `Invalid API URL: ${apiUrl}`
    };
  }
  
  return { hasCorsIssues: false };
}

/**
 * Validate authentication setup
 */
export function validateAuthSetup(accessToken?: string | null): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check access token
  if (!accessToken) {
    issues.push('No access token available');
    recommendations.push('Try signing in again or refreshing the session');
  } else if (accessToken.length < 10) {
    issues.push('Access token appears to be too short');
    recommendations.push('Check if the token is properly generated');
  }
  
  // Check refresh token cookie
  const debugInfo = getAuthDebugInfo(accessToken);
  if (!debugInfo.hasRefreshTokenCookie) {
    issues.push('No refresh token cookie found');
    recommendations.push('Check if refresh token cookie is being set properly by the backend');
  }
  
  // Check CORS
  const corsCheck = checkCorsIssues();
  if (corsCheck.hasCorsIssues) {
    issues.push(`CORS issue detected: ${corsCheck.reason}`);
    recommendations.push('Ensure CORS is properly configured on the backend');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    recommendations
  };
}
