import { UserProfile, Workspace, WorkspaceStatus } from '@/types';

export interface WorkspaceRouteConfig {
  defaultRoute: string;
  onboardingRoute: string;
  suspendedRoute: string;
  trialExpiredRoute: string;
}

const DEFAULT_ROUTES: WorkspaceRouteConfig = {
  defaultRoute: '/dashboard',
  onboardingRoute: '/onboarding',
  suspendedRoute: '/workspace/suspended',
  trialExpiredRoute: '/workspace/trial-expired'
};

/**
 * Determines the appropriate route for a user based on their workspace status
 * and onboarding completion
 */
export const getWorkspaceRoute = (
  workspace: Workspace | null,
  profile: UserProfile | null,
  config: Partial<WorkspaceRouteConfig> = {}
): string => {
  const routes = { ...DEFAULT_ROUTES, ...config };

  if (!workspace || !profile) {
    return routes.defaultRoute;
  }

  // Check workspace status first
  switch (workspace.status) {
    case WorkspaceStatus.SUSPENDED:
      return routes.suspendedRoute;
    
    case WorkspaceStatus.CANCELLED:
      return routes.suspendedRoute;
    
    case WorkspaceStatus.TRIAL:
      // Check if trial has expired
      if (workspace.trial_ends_at) {
        const trialEndDate = new Date(workspace.trial_ends_at);
        const now = new Date();
        if (now > trialEndDate) {
          return routes.trialExpiredRoute;
        }
      }
      break;
  }

  // Check onboarding completion
  if (!workspace.onboarding_completed) {
    return routes.onboardingRoute;
  }

  // Default to dashboard
  return routes.defaultRoute;
};

/**
 * Checks if a workspace is accessible for normal operations
 */
export const isWorkspaceAccessible = (workspace: Workspace | null): boolean => {
  if (!workspace) return false;

  // Suspended or cancelled workspaces are not accessible
  if (workspace.status === WorkspaceStatus.SUSPENDED || 
      workspace.status === WorkspaceStatus.CANCELLED) {
    return false;
  }

  // Check trial expiration
  if (workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at) {
    const trialEndDate = new Date(workspace.trial_ends_at);
    const now = new Date();
    if (now > trialEndDate) {
      return false;
    }
  }

  return true;
};

/**
 * Gets workspace status information for display
 */
export const getWorkspaceStatusInfo = (workspace: Workspace | null) => {
  if (!workspace) {
    return {
      status: 'unknown',
      message: 'Workspace not found',
      severity: 'error' as const
    };
  }

  switch (workspace.status) {
    case WorkspaceStatus.ACTIVE:
      return {
        status: 'active',
        message: 'Workspace is active',
        severity: 'success' as const
      };

    case WorkspaceStatus.TRIAL:
      if (workspace.trial_ends_at) {
        const trialEndDate = new Date(workspace.trial_ends_at);
        const now = new Date();
        const daysLeft = Math.ceil((trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysLeft <= 0) {
          return {
            status: 'trial_expired',
            message: 'Trial period has expired',
            severity: 'error' as const
          };
        } else if (daysLeft <= 3) {
          return {
            status: 'trial_expiring',
            message: `Trial expires in ${daysLeft} day${daysLeft === 1 ? '' : 's'}`,
            severity: 'warning' as const
          };
        } else {
          return {
            status: 'trial_active',
            message: `Trial active (${daysLeft} days remaining)`,
            severity: 'info' as const
          };
        }
      }
      return {
        status: 'trial_active',
        message: 'Trial period active',
        severity: 'info' as const
      };

    case WorkspaceStatus.SUSPENDED:
      return {
        status: 'suspended',
        message: 'Workspace is suspended',
        severity: 'error' as const
      };

    case WorkspaceStatus.CANCELLED:
      return {
        status: 'cancelled',
        message: 'Workspace has been cancelled',
        severity: 'error' as const
      };

    default:
      return {
        status: 'unknown',
        message: 'Unknown workspace status',
        severity: 'warning' as const
      };
  }
};

/**
 * Validates if a user can access a specific workspace
 */
export const canAccessWorkspace = (
  workspace: Workspace | null,
  profile: UserProfile | null
): boolean => {
  if (!workspace || !profile) return false;
  
  // Check if profile belongs to the workspace
  if (profile.workspace_id !== workspace.id) return false;
  
  // Check if user is active
  if (!profile.is_active) return false;
  
  // Check workspace accessibility
  return isWorkspaceAccessible(workspace);
};