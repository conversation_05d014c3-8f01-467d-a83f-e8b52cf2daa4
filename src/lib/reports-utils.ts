import { Quarter } from "@/components/ui/quarter-selector";
import { ListingResponse } from "@/types/api";

/**
 * Get the current quarter based on today's date
 */
export function getCurrentQuarter(): Quarter {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth(); // 0-based (0 = January)
  const quarter = Math.floor(month / 3) + 1; // 1-4
  
  const startMonth = (quarter - 1) * 3;
  const endMonth = startMonth + 2;
  
  return {
    id: `${year}-Q${quarter}`,
    label: `Q${quarter} ${year}`,
    year,
    quarter,
    startDate: new Date(year, startMonth, 1),
    endDate: new Date(year, endMonth + 1, 0), // Last day of the quarter
  };
}

/**
 * Convert a Quarter object to API filter parameters
 */
export function quarterToDateRange(quarter: Quarter) {
  return {
    fromDate: quarter.startDate.toISOString().split('T')[0], // YYYY-MM-DD format
    toDate: quarter.endDate.toISOString().split('T')[0],
  };
}

/**
 * Convert date range to API filter parameters
 */
export function dateRangeToApiParams(from?: Date, to?: Date) {
  const params: Record<string, string> = {};
  
  if (from) {
    params.fromDate = from.toISOString().split('T')[0];
  }
  
  if (to) {
    params.toDate = to.toISOString().split('T')[0];
  }
  
  return params;
}

/**
 * Calculate overview metrics from listing data
 */
export interface OverviewMetrics {
  totalListings: number;
  activeListings: number;
  underContract: number;
  closedDeals: number;
  draftListings: number;
  totalCommissions: number;
  totalRevenue: number;
  avgDaysOnMarket: number;
  conversionRate: number;
  avgDealSize: number;
}

export function calculateOverviewMetrics(listings: ListingResponse[]): OverviewMetrics {
  // Handle empty or invalid input
  if (!listings || !Array.isArray(listings)) {
    return {
      totalListings: 0,
      activeListings: 0,
      underContract: 0,
      closedDeals: 0,
      draftListings: 0,
      totalCommissions: 0,
      totalRevenue: 0,
      avgDaysOnMarket: 0,
      conversionRate: 0,
      avgDealSize: 0,
    };
  }

  const totalListings = listings.length;
  const activeListings = listings.filter(l => l.status === 'Active').length;
  const underContract = listings.filter(l => l.status === 'Under Contract').length;
  const closedDeals = listings.filter(l => l.status === 'Sold').length;
  const draftListings = listings.filter(l => l.status === 'Draft').length;
  
  // Calculate revenue from sold listings
  const soldListings = listings.filter(l => l.status === 'Sold');
  const totalRevenue = soldListings.reduce((sum, listing) => sum + (listing.askingPrice || 0), 0);
  
  // Calculate commissions (assuming 3% commission rate as default)
  const totalCommissions = soldListings.reduce((sum, listing) => {
    const commissionRate = listing.commissionRate || 0.03; // 3% default
    return sum + (listing.askingPrice || 0) * commissionRate;
  }, 0);
  
  // Calculate average days on market
  const listingsWithDays = listings.filter(l => l.daysListed && l.daysListed > 0);
  const avgDaysOnMarket = listingsWithDays.length > 0 
    ? listingsWithDays.reduce((sum, l) => sum + (l.daysListed || 0), 0) / listingsWithDays.length
    : 0;
  
  // Calculate conversion rate (closed deals / total listings)
  const conversionRate = totalListings > 0 ? (closedDeals / totalListings) * 100 : 0;
  
  // Calculate average deal size
  const avgDealSize = closedDeals > 0 ? totalRevenue / closedDeals : 0;
  
  return {
    totalListings,
    activeListings,
    underContract,
    closedDeals,
    draftListings,
    totalCommissions,
    totalRevenue,
    avgDaysOnMarket: Math.round(avgDaysOnMarket),
    conversionRate: Math.round(conversionRate * 100) / 100, // Round to 2 decimal places
    avgDealSize,
  };
}

/**
 * Group listings by month for performance analysis
 */
export interface MonthlyPerformance {
  month: string;
  year: number;
  monthName: string;
  listings: number;
  closed: number;
  revenue: number;
  commissions: number;
}

export function calculateMonthlyPerformance(listings: ListingResponse[]): MonthlyPerformance[] {
  // Handle empty or invalid input
  if (!listings || !Array.isArray(listings)) {
    return [];
  }

  const monthlyData = new Map<string, MonthlyPerformance>();

  listings.forEach(listing => {
    // Skip listings without valid creation date
    if (!listing.createdAt) {
      return;
    }
    const createdDate = new Date(listing.createdAt);
    const monthKey = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}`;
    const monthName = createdDate.toLocaleDateString('en-US', { month: 'short' });
    
    if (!monthlyData.has(monthKey)) {
      monthlyData.set(monthKey, {
        month: monthKey,
        year: createdDate.getFullYear(),
        monthName,
        listings: 0,
        closed: 0,
        revenue: 0,
        commissions: 0,
      });
    }
    
    const monthData = monthlyData.get(monthKey)!;
    monthData.listings += 1;
    
    if (listing.status === 'Sold') {
      monthData.closed += 1;
      monthData.revenue += listing.askingPrice || 0;
      const commissionRate = listing.commissionRate || 0.03;
      monthData.commissions += (listing.askingPrice || 0) * commissionRate;
    }
  });
  
  // Convert to array and sort by month
  return Array.from(monthlyData.values()).sort((a, b) => a.month.localeCompare(b.month));
}

/**
 * Filter listings by status
 */
export function filterListingsByStatus(listings: ListingResponse[], status?: string): ListingResponse[] {
  if (!status || status === 'all') {
    return listings;
  }
  return listings.filter(listing => listing.status === status);
}

/**
 * Filter listings by industry
 */
export function filterListingsByIndustry(listings: ListingResponse[], industry?: string): ListingResponse[] {
  if (!industry || industry === 'all') {
    return listings;
  }
  return listings.filter(listing => listing.industry === industry);
}

/**
 * Filter listings by search term (searches business name and industry)
 */
export function filterListingsBySearch(listings: ListingResponse[], searchTerm?: string): ListingResponse[] {
  if (!searchTerm || searchTerm.trim() === '') {
    return listings;
  }
  
  const term = searchTerm.toLowerCase().trim();
  return listings.filter(listing => 
    listing.businessName.toLowerCase().includes(term) ||
    listing.industry.toLowerCase().includes(term) ||
    (listing.generalLocation && listing.generalLocation.toLowerCase().includes(term))
  );
}

/**
 * Get unique industries from listings
 */
export function getUniqueIndustries(listings: ListingResponse[]): string[] {
  const industries = new Set(listings.map(listing => listing.industry));
  return Array.from(industries).sort();
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Format percentage for display
 */
export function formatPercentage(value: number): string {
  return `${value.toFixed(1)}%`;
}

/**
 * Calculate days between two dates
 */
export function daysBetween(date1: Date, date2: Date): number {
  const diffTime = Math.abs(date2.getTime() - date1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Validate and sanitize listing data
 */
export function validateListingData(listings: any[]): ListingResponse[] {
  if (!Array.isArray(listings)) {
    return [];
  }

  return listings.filter(listing =>
    listing &&
    typeof listing === 'object' &&
    listing.id &&
    listing.businessName &&
    listing.status
  );
}

/**
 * Check if data is loading or empty
 */
export function isDataEmpty(data: any[]): boolean {
  return !data || !Array.isArray(data) || data.length === 0;
}

/**
 * Get status color for charts and displays
 */
export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'active':
      return '#10b981'; // emerald-500
    case 'under contract':
      return '#f59e0b'; // amber-500
    case 'sold':
      return '#3b82f6'; // blue-500
    case 'draft':
      return '#6b7280'; // gray-500
    default:
      return '#6b7280'; // gray-500
  }
}
