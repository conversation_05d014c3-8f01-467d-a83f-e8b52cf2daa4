/**
 * Utility functions for handling status-related styling and mapping
 */

export type ListingStatus = 
  | "active" 
  | "under contract" 
  | "sold" 
  | "draft" 
  | "confidential" 
  | "archived" 
  | "pending" 
  | "withdrawn" 
  | "expired";

export type StatusVariant = 
  | "status-active"
  | "status-under-contract" 
  | "status-sold"
  | "status-draft"
  | "status-confidential"
  | "status-archived"
  | "solid-active"
  | "solid-under-contract"
  | "solid-sold"
  | "solid-draft"
  | "solid-confidential"
  | "solid-archived";

/**
 * Maps listing status to glassmorphic badge variant
 */
export function getStatusBadgeVariant(status: string, useGlass: boolean = true): StatusVariant {
  const normalizedStatus = status.toLowerCase().trim();
  
  const prefix = useGlass ? "status-" : "solid-";
  
  switch (normalizedStatus) {
    case "active":
      return `${prefix}active` as StatusVariant;
    case "under contract":
      return `${prefix}under-contract` as StatusVariant;
    case "sold":
    case "closed":
      return `${prefix}sold` as StatusVariant;
    case "draft":
      return `${prefix}draft` as StatusVariant;
    case "confidential":
      return `${prefix}confidential` as StatusVariant;
    case "archived":
      return `${prefix}archived` as StatusVariant;
    default:
      return `${prefix}draft` as StatusVariant; // Default fallback
  }
}

/**
 * Gets human-readable status label
 */
export function getStatusLabel(status: string): string {
  const normalizedStatus = status.toLowerCase().trim();
  
  switch (normalizedStatus) {
    case "active":
      return "Active";
    case "under contract":
      return "Under Contract";
    case "sold":
    case "closed":
      return "Sold";
    case "draft":
      return "Draft";
    case "confidential":
      return "Confidential";
    case "archived":
      return "Archived";
    case "pending":
      return "Pending";
    case "withdrawn":
      return "Withdrawn";
    case "expired":
      return "Expired";
    default:
      return status; // Return original if no mapping found
  }
}

/**
 * Status configuration for comprehensive mapping
 */
export const statusConfig = {
  "active": { 
    color: "bg-emerald-500", 
    label: "Active",
    glassVariant: "status-active" as StatusVariant,
    solidVariant: "solid-active" as StatusVariant
  },
  "under contract": { 
    color: "bg-amber-500", 
    label: "Under Contract",
    glassVariant: "status-under-contract" as StatusVariant,
    solidVariant: "solid-under-contract" as StatusVariant
  },
  "sold": { 
    color: "bg-blue-600", 
    label: "Sold",
    glassVariant: "status-sold" as StatusVariant,
    solidVariant: "solid-sold" as StatusVariant
  },
  "confidential": { 
    color: "bg-purple-600", 
    label: "Confidential",
    glassVariant: "status-confidential" as StatusVariant,
    solidVariant: "solid-confidential" as StatusVariant
  },
  "archived": { 
    color: "bg-slate-500", 
    label: "Archived",
    glassVariant: "status-archived" as StatusVariant,
    solidVariant: "solid-archived" as StatusVariant
  },
  "draft": { 
    color: "bg-gray-500", 
    label: "Draft",
    glassVariant: "status-draft" as StatusVariant,
    solidVariant: "solid-draft" as StatusVariant
  },
  "pending": { 
    color: "bg-orange-500", 
    label: "Pending",
    glassVariant: "status-draft" as StatusVariant, // Fallback to draft styling
    solidVariant: "solid-draft" as StatusVariant
  },
  "withdrawn": { 
    color: "bg-red-500", 
    label: "Withdrawn",
    glassVariant: "status-draft" as StatusVariant, // Fallback to draft styling
    solidVariant: "solid-draft" as StatusVariant
  },
  "expired": { 
    color: "bg-rose-500", 
    label: "Expired",
    glassVariant: "status-draft" as StatusVariant, // Fallback to draft styling
    solidVariant: "solid-draft" as StatusVariant
  },
} as const;

/**
 * Gets status configuration for a given status
 */
export function getStatusConfig(status: string) {
  const normalizedStatus = status.toLowerCase().trim();
  return statusConfig[normalizedStatus as keyof typeof statusConfig] || statusConfig.draft;
}
