export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

// Helper function to safely format currency from string or number
export const formatCurrencySafe = (value: string | number): string => {
  if (typeof value === 'string') {
    const num = parseCurrency(value);
    return num !== undefined ? formatCurrency(num) : '$0';
  }
  return formatCurrency(value);
};

// Format currency with K, M, B abbreviations for large numbers
export const formatCurrencyAbbreviated = (value: string | number): string => {
  let num: number;
  
  if (typeof value === 'string') {
    const parsed = parseCurrency(value);
    if (parsed === undefined) return '$0';
    num = parsed;
  } else {
    num = value;
  }

  if (num >= 1000000000) {
    const billions = num / 1000000000;
    return `$${billions.toFixed(billions >= 10 ? 1 : 2)}B`;
  } else if (num >= 1000000) {
    const millions = num / 1000000;
    return `$${millions.toFixed(millions >= 10 ? 1 : 2)}M`;
  } else if (num >= 1000) {
    const thousands = num / 1000;
    return `$${thousands.toFixed(thousands >= 10 ? 0 : 1)}K`;
  } else {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(num);
  }
};

export const formatDate = (date: string | Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(new Date(date));
};

// Form-specific currency utilities
export const parseCurrency = (value: string): number | undefined => {
  const cleaned = value.replace(/[^0-9.]/g, '');
  const num = parseFloat(cleaned);
  return isNaN(num) ? undefined : num;
};

export const formatCurrencyInput = (value: string): string => {
  const num = parseCurrency(value);
  if (num === undefined) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(num);
};