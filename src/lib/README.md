# API Configuration Library

This directory contains the API configuration and client utilities for the application.

## 🤖 Instructions for LLMs

**IMPORTANT**: Before making any changes to API endpoints or implementing new API functionality, always check the latest API documentation by making a request to:

```
{VITE_API_URL}/doc
```

Where `VITE_API_URL` is the environment variable containing the API base URL (defaults to `http://localhost:3001`).

### How to Check API Documentation

1. **Get the current API base URL** from `api-config.ts` or environment variables
2. **Make a request** to `{API_BASE_URL}/doc` to get the OpenAPI specification
3. **Parse the response** to understand available endpoints, request/response formats, and required fields
4. **Update configurations** accordingly in `api-config.ts` and `api-client.ts`

### Example Commands

```bash
# Check API documentation
curl -s http://localhost:3001/doc | jq '.'

# Get specific endpoint information
curl -s http://localhost:3001/doc | jq '.paths."/v1/auth/sign-up/email"'

# Get required fields for signup
curl -s http://localhost:3001/doc | jq '.paths."/v1/auth/sign-up/email".post.requestBody.content."application/json".schema.required'
```

## Hono Type-Safe Client Setup

This project is configured to use Hono's type-safe RPC client for end-to-end type safety between frontend and backend.

### Current Status
- ✅ Hono client dependencies installed
- ✅ Placeholder API client with fallback to fetch
- ✅ Example implementation in `typed-api-client.ts`
- ⏳ Waiting for backend AppType export

### Setup Instructions

1. **Backend Setup** (when backend is available):
   ```typescript
   // In your Hono backend server file
   import { Hono } from 'hono';
   
    const app = new Hono()
      .get('/v1/auth/signin', ...)
      .post('/v1/auth/sign-up/email', ...)
     // ... other routes

   export type AppType = typeof app;
   ```

2. **Frontend Integration**:
   ```typescript
   // Update src/lib/api-client.ts
   import type { AppType } from 'path/to/backend/server';
   
   const client = hc<AppType>(API_CONFIG.BASE_URL);
   ```

3. **Replace API Methods**:
   Use the examples in `typed-api-client.ts` to replace the current fetch-based methods with type-safe Hono client calls.

### Benefits of Type-Safe Client

- **Compile-time type checking** for API requests/responses
- **Auto-completion** for API endpoints and parameters
- **Automatic type inference** from backend route definitions
- **Refactoring safety** - changes in backend automatically surface in frontend
- **Better developer experience** with IntelliSense support

### Migration Steps

1. **Phase 1** (Current): Use existing fetch-based client
2. **Phase 2**: Import backend AppType when available
3. **Phase 3**: Replace API methods with typed Hono client calls
4. **Phase 4**: Remove legacy fetch-based implementation

## File Overview

- **`api-config.ts`** - Central configuration for API endpoints and settings
- **`api-client.ts`** - HTTP client utility with typed methods for API calls (currently fetch-based)
- **`typed-api-client.ts`** - Example implementation of Hono type-safe client (for future use)
- **`supabase.ts`** - Supabase client configuration (legacy, being phased out)
- **`utils.ts`** - General utility functions
- **`formatters.ts`** - Data formatting utilities
- **`workspace-routing.ts`** - Workspace-specific routing logic

## Key Principles

1. **Always verify endpoints** against the live API documentation before implementing
2. **Use TypeScript interfaces** that match the API response schemas
3. **Handle errors gracefully** with proper error mapping
4. **Maintain backward compatibility** when possible
5. **Update both config and client** when API changes occur
6. **Prefer type-safe clients** over manual fetch calls

## Authentication Flow

The application uses a custom authentication API instead of Supabase auth. Key endpoints:

- `POST /signup` - User registration with workspace creation
- `POST /signin` - User authentication
- `POST /signout` - User logout
- `POST /refresh` - Token refresh
- `POST /forgot-password` - Password reset request
- `POST /reset-password` - Password reset confirmation

**Note**: Endpoint paths may have version prefixes (e.g., `/v1/auth/sign-up/email`). Always check the current API documentation for the exact paths.

## Environment Variables

- `VITE_API_URL` - Base URL for the API server (default: `http://localhost:3001`)

## Best Practices

1. **Check API docs first** - Always verify current endpoint specifications
2. **Type safety** - Use TypeScript interfaces that match API schemas
3. **Error handling** - Map API errors to user-friendly messages
4. **Session management** - Store session data securely and handle expiration
5. **Backward compatibility** - Support migration from legacy authentication methods
6. **Use Hono RPC** - Prefer type-safe client over manual fetch when backend types are available

---

**Remember**: The API is the source of truth. When in doubt, check `{VITE_API_URL}/doc` for the latest specifications. 