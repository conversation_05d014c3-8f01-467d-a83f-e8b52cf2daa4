import { AuthErrorCode } from '@/types';

// Global navigation and toast functions that will be set by the app
let globalNavigate: ((path: string, options?: { replace?: boolean }) => void) | null = null;
let globalToast: ((options: {
  title: string;
  description: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}) => void) | null = null;
let globalDismissToast: (() => void) | null = null;

// Function to set the global handlers (called from App.tsx or a top-level component)
export const setTokenExpirationHandlers = (
  navigate: (path: string, options?: { replace?: boolean }) => void,
  toast: (options: { title: string; description: string; variant?: 'default' | 'destructive'; duration?: number; }) => void,
  dismissToast: () => void
) => {
  globalNavigate = navigate;
  globalToast = toast;
  globalDismissToast = dismissToast;
};

// Function to check if an error is a token expiration error
export const isTokenExpiredError = (error: any): boolean => {
  // Check if it's a session expired error
  if (error?.code === AuthErrorCode.SESSION_EXPIRED) {
    return true;
  }

  // Check if it's a 401 error from API
  if (error?.status === 401) {
    return true;
  }

  // Check for common token expiration messages
  if (typeof error === 'string' || error?.message) {
    const message = (error?.message || error).toLowerCase();
    return message.includes('session expired') || 
           message.includes('token expired') ||
           message.includes('unauthorized') ||
           message.includes('invalid token');
  }

  return false;
};

// Function to handle token expiration globally
export const handleTokenExpiration = (error?: any, redirectTo: string = '/') => {
  if (!globalNavigate || !globalToast || !globalDismissToast) {
    console.warn('Token expiration handlers not set. Call setTokenExpirationHandlers first.');
    return;
  }

  globalDismissToast();

  globalNavigate!(redirectTo, { replace: true });


};

// Function to handle unauthorized access (different from token expiration)
export const handleUnauthorizedAccess = (message?: string, redirectTo: string = '/unauthorized') => {
  if (!globalNavigate || !globalToast || !globalDismissToast) {
    console.warn('Token expiration handlers not set. Call setTokenExpirationHandlers first.');
    return;
  }

  // Clear any pending toasts
  globalDismissToast();

  // Show unauthorized access toast
  globalToast({
    title: "Access Denied",
    description: message || "You don't have permission to access this resource.",
    variant: "destructive",
    duration: 3000, // 3 seconds
  });

  // Navigate to unauthorized page after a brief delay to ensure toast is visible
  setTimeout(() => {
    globalNavigate!(redirectTo, { replace: true });
  }, 100);
};