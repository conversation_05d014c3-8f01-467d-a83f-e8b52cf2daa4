import { useQueryClient } from '@tanstack/react-query'
import { toast } from '@/hooks/use-toast'
import { isApiError } from './api-client'
import { handleTokenExpiration, handleUnauthorizedAccess, isTokenExpiredError } from './token-expiration-handler'

// Query keys for consistent caching
export const queryKeys = {
  // Auth
  session: () => ['auth', 'session'] as const,
  user: () => ['auth', 'user'] as const,
  userProfile: () => ['user', 'profile'] as const,

  // Listings
  listings: (params?: Record<string, any>) =>
    params ? ['listings', params] as const : ['listings'] as const,
  listing: (id: string) => ['listing', id] as const,

  // Workspace
  workspaces: () => ['workspaces'] as const,
  workspace: (id: string) => ['workspace', id] as const,

  // Team
  teamMembers: (workspaceId: string) => ['team', 'members', workspaceId] as const,
  teamInvitations: (workspaceId: string) => ['team', 'invitations', workspaceId] as const,

  // Files
  files: (listingId?: string) =>
    listingId ? ['files', 'listing', listingId] as const : ['files'] as const,
} as const

// Note: These functions are deprecated and should not be used.
// Use the useAuth hook from AuthContext instead to get the access token.
// They are kept here temporarily for backward compatibility.

// Get access token from localStorage (DEPRECATED - use useAuth hook instead)
export function getAccessToken(): string | null {
  console.warn('getAccessToken from query-client is deprecated. Use useAuth hook instead.');
  return null; // Always return null to force migration to useAuth
}

// Auth guard for queries - throws error if no token (DEPRECATED - use useAuth hook instead)
export function requireAuth(): string {
  console.warn('requireAuth from query-client is deprecated. Use useAuth hook instead.');
  throw new Error('No access token available. Please use useAuth hook to get the access token.');
}

// Standard error handler for React Query
export function handleQueryError(error: unknown) {
  console.error('Query error:', error)
  
  // Check for token expiration first
  if (isTokenExpiredError(error)) {
    handleTokenExpiration(error);
    return 'Session expired'; // Return a message but don't show toast (handled globally)
  }
  
  let errorMessage = 'An unexpected error occurred'
  
  if (isApiError(error)) {
    errorMessage = error.message
    
    // Handle specific error cases
    if (error.status === 401) {
      errorMessage = 'Authentication failed. Please log in again.'
    } else if (error.status === 403) {
      // Handle 403 Forbidden as unauthorized access (not token expiration)
      handleUnauthorizedAccess('You do not have permission to perform this action.');
      return 'Access denied'; // Return a message but don't show toast (handled globally)
    } else if (error.status === 404) {
      errorMessage = 'The requested resource was not found.'
    } else if (error.status === 422) {
      errorMessage = 'Invalid data provided. Please check your input.'
    } else if (error.status >= 500) {
      errorMessage = 'Server error. Please try again later.'
    }
  } else if (error instanceof Error) {
    errorMessage = error.message
  }

  return errorMessage
}

// Standard success handler for mutations
export function handleMutationSuccess(message: string) {
  toast({
    title: 'Success',
    description: message,
  })
}

// Standard error handler for mutations
export function handleMutationError(error: unknown) {
  const errorMessage = handleQueryError(error)
  toast({
    title: 'Error',
    description: errorMessage,
    variant: 'destructive',
  })
}

// Hook to invalidate related queries after mutations
export function useInvalidateQueries() {
  const queryClient = useQueryClient()
  
  return {
    // Invalidate auth data
    invalidateSession: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.session() })
    },

    invalidateUser: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.user() })
    },

    // Invalidate all auth-related data
    invalidateAuth: () => {
      queryClient.invalidateQueries({ queryKey: ['auth'] })
      queryClient.invalidateQueries({ queryKey: ['user'] })
    },

    // Invalidate all listings
    invalidateListings: () => {
      queryClient.invalidateQueries({ queryKey: ['listings'] })
    },

    // Invalidate specific listing
    invalidateListing: (id: string) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.listing(id) })
    },

    // Invalidate user profile
    invalidateUserProfile: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile() })
    },

    // Invalidate team data
    invalidateTeam: (workspaceId: string) => {
      queryClient.invalidateQueries({ queryKey: ['team'] })
      queryClient.invalidateQueries({ queryKey: queryKeys.teamMembers(workspaceId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.teamInvitations(workspaceId) })
    },
    
    // Invalidate all queries (use sparingly)
    invalidateAll: () => {
      queryClient.invalidateQueries()
    },
  }
}

// Helper to update query data optimistically
export function useOptimisticUpdates() {
  const queryClient = useQueryClient()
  
  return {
    // Update listings query data
    updateListingsData: (updater: (oldData: any) => any) => {
      queryClient.setQueryData(['listings'], updater)
    },
    
    // Update specific listing
    updateListingData: (id: string, updater: (oldData: any) => any) => {
      queryClient.setQueryData(queryKeys.listing(id), updater)
    },
    
    // Add new listing to listings cache
    addListingToCache: (newListing: any) => {
      queryClient.setQueryData(['listings'], (oldData: any) => {
        if (!oldData) return oldData
        return {
          ...oldData,
          data: [newListing, ...(oldData.data || [])],
          pagination: {
            ...oldData.pagination,
            total: (oldData.pagination?.total || 0) + 1,
          },
        }
      })
    },
    
    // Remove listing from cache
    removeListingFromCache: (id: string) => {
      queryClient.setQueryData(['listings'], (oldData: any) => {
        if (!oldData) return oldData
        return {
          ...oldData,
          data: (oldData.data || []).filter((listing: any) => listing.id !== id),
          pagination: {
            ...oldData.pagination,
            total: Math.max((oldData.pagination?.total || 1) - 1, 0),
          },
        }
      })
      
      // Remove the individual listing from cache too
      queryClient.removeQueries({ queryKey: queryKeys.listing(id) })
    },
  }
} 