import { UserRole } from "@/types";

/**
 * Mapping of role values to their display labels
 */
export const ROLE_LABELS: Record<UserRole, string> = {
  [UserRole.OWNER]: "Owner",
  [UserRole.ADMIN]: "Admin",
  [UserRole.MEMBER]: "Member",
  // [UserRole.VIEWER]: 'Viewer',
};

/**
 * Get the display label for a role
 * @param role - The role value
 * @returns The capitalized display label
 */
export const getRoleLabel = (role: string): string => {
  return (
    ROLE_LABELS[role as UserRole] ||
    role.charAt(0).toUpperCase() + role.slice(1)
  );
};
