import { API_CONFIG, API_ENDPOINTS } from "./api-config";
import { createAuthClient } from "better-auth/react";
import { magicLinkClient } from "better-auth/client/plugins";
import type { components } from "@/types/api";
import type {
  Workspace as AppWorkspace,
  UserProfile as AppUserProfile,
  AuthSession as AppAuthSession,
} from "@/types";

// Import types from generated API types
export type AuthResponse = components["schemas"]["AuthResponse"];
export type SignUpRequest = components["schemas"]["SignUpRequest"];
export type SignInRequest = components["schemas"]["SignInRequest"];

// Session response type for the /get-session endpoint
export interface SessionResponse {
  session: {
    id: string;
    userId: string;
    activeOrganizationId?: string | null;
    expiresAt: string;
    createdAt: string;
    updatedAt: string;
  };
  user: {
    id: string;
    email: string;
    name?: string;
    emailVerified: boolean;
    image?: string;
    createdAt: string;
    updatedAt: string;
  };
}

// Helper function to convert AuthResponse to SessionResponse format
export function authResponseToSessionResponse(
  authResponse: AuthResponse
): SessionResponse {
  return {
    session: {
      id: authResponse.session?.id || "",
      userId: authResponse.session?.userId || "",
      activeOrganizationId: authResponse.session?.activeOrganizationId || null,
      expiresAt: authResponse.session?.expiresAt || "",
      createdAt: authResponse.session?.createdAt || "",
      updatedAt: authResponse.session?.updatedAt || "",
    },
    user: {
      id: authResponse.user.id,
      email: authResponse.user.email,
      name: authResponse.user.name || undefined,
      emailVerified: authResponse.user.emailVerified,
      image: authResponse.user.image || undefined,
      createdAt: authResponse.user.createdAt,
      updatedAt: authResponse.user.updatedAt,
    },
  };
}

// App-level types (from our domain models)
export interface User {
  id: string;
  email?: string;
  [key: string]: unknown;
}
export type Workspace = AppWorkspace;
export type UserProfile = AppUserProfile;
export type AuthSession = AppAuthSession;

// Profile types (from API schemas)
export type UpdateProfileRequest =
  components["schemas"]["UpdateProfileRequest"];
export type UpdateProfileResponse =
  components["schemas"]["UpdateProfileResponse"];

// File types
export type UploadedFile = components["schemas"]["File"];
export type UploadFileRequest = components["schemas"]["UploadFileRequest"];
export type UploadFileResponse = components["schemas"]["UploadFileResponse"];
export type GetFileResponse = components["schemas"]["GetFileResponse"];
export type DeleteResponse = components["schemas"]["DeleteResponse"];

// Listing types
export type ListingResponse = components["schemas"]["ListingResponse"];
export type ListingListResponse = components["schemas"]["ListingListResponse"];
export type SingleListingResponse =
  components["schemas"]["SingleListingResponse"];
export type CreateListingRequest =
  components["schemas"]["CreateListingRequest"];
export type UpdateListingRequest =
  components["schemas"]["UpdateListingRequest"];
export type SaveDraftListingRequest =
  components["schemas"]["SaveDraftListingRequest"];
export type BulkCreateResponse = components["schemas"]["BulkCreateResponse"];

// Workspace types (mapped to app domain models or lightweight placeholders)
export type SingleWorkspaceResponse = Workspace;
export type WorkspaceUpdateResponse = Workspace;
export type UpdateWorkspaceRequest = Partial<Workspace>;
export type WorkspaceInvitation = import("@/types").WorkspaceInvitation;
export type WorkspaceInvitationListResponse = { data: WorkspaceInvitation[] };
export type SingleWorkspaceInvitationResponse = { data: WorkspaceInvitation };
export type CreateWorkspaceInvitationRequest = {
  email: string;
  role: string;
  redirectTo?: string;
};
export type UpdateWorkspaceInvitationRequest = {
  role?: string;
  status?: string;
};

// Convenient type alias
export type Listing = ListingResponse;

// Legacy types for backward compatibility (remove once all usage is updated)
export interface UserWorkspacesResponse {
  workspaces: {
    workspace_id: string;
    workspace_name: string;
    company_name: string;
    user_role: string;
    is_active: boolean;
    status: string;
    subscription_plan: string;
    logo_url?: string;
    primary_color?: string;
  }[];
}

export interface SwitchWorkspaceRequest {
  workspace_id: string;
}

export interface SwitchWorkspaceResponse {
  success: boolean;
  workspace: {
    id: string;
    companyName: string;
    companyType: string;
    subscriptionPlan: string;
    status: string;
    createdAt: string;
  };
  profile: {
    id: string;
    workspaceId: string;
    role: string;
    firstName: string;
    lastName: string;
    displayName: string;
    email: string;
  };
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Lightweight API client used by hooks and components
class ApiClient {
  private readonly baseUrl: string;
  private readonly usePlaceholder: boolean;

  constructor(baseUrl: string = API_CONFIG.BASE_URL) {
    this.baseUrl = baseUrl;
    // Use placeholder API if no backend is available or in development
    this.usePlaceholder =
      import.meta.env.VITE_USE_PLACEHOLDER_API === "true" ||
      import.meta.env.DEV;
  }

  private async request<T>(path: string, init: RequestInit = {}): Promise<T> {
    // Use placeholder API if enabled
    if (this.usePlaceholder) {
      return this.placeholderRequest<T>(path, init);
    }

    const response = await fetch(`${this.baseUrl}${path}`, {
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        ...(init.headers || {}),
      },
      ...init,
    });

    const isJson = response.headers
      .get("content-type")
      ?.includes("application/json");
    const data = isJson ? await response.json() : await response.text();

    if (!response.ok) {
      const message =
        (data as any)?.message || response.statusText || "Request failed";
      const error: ApiError = { message, status: response.status };
      throw error;
    }

    return data as T;
  }

  private async placeholderRequest<T>(
    path: string,
    init: RequestInit = {}
  ): Promise<T> {
    // Log placeholder API usage in development
    if (import.meta.env.DEV) {
      console.log(`🔧 Placeholder API: ${init.method || "GET"} ${path}`);
    }

    // Simulate network delay
    await new Promise((resolve) =>
      setTimeout(resolve, 200 + Math.random() * 300)
    );

    const method = init.method || "GET";
    const body = init.body ? JSON.parse(init.body as string) : null;

    // Generate placeholder responses based on endpoint
    const response = this.generatePlaceholderResponse(path, method, body);

    if (!response) {
      throw new Error(
        `Placeholder API: Endpoint not implemented: ${method} ${path}`
      );
    }

    return response as T;
  }

  private generatePlaceholderResponse(
    path: string,
    method: string,
    body: any
  ): any {
    const now = new Date().toISOString();
    const userId = "placeholder-user-123";
    const workspaceId = "placeholder-workspace-456";

    // Helper function to generate realistic listing data
    const generateListing = (id: string, overrides: any = {}) => ({
      id,
      title: overrides.title || "Beautiful Family Home",
      description:
        overrides.description ||
        "A stunning property in a great location with modern amenities and excellent curb appeal.",
      price: overrides.price || Math.floor(Math.random() * 500000) + 300000,
      propertyType:
        overrides.propertyType ||
        ["house", "condo", "townhouse", "apartment"][
          Math.floor(Math.random() * 4)
        ],
      bedrooms: overrides.bedrooms || Math.floor(Math.random() * 4) + 1,
      bathrooms: overrides.bathrooms || Math.floor(Math.random() * 3) + 1,
      squareFootage:
        overrides.squareFootage || Math.floor(Math.random() * 2000) + 800,
      lotSize: overrides.lotSize || Math.floor(Math.random() * 10000) + 5000,
      yearBuilt: overrides.yearBuilt || Math.floor(Math.random() * 50) + 1970,
      address: overrides.address || {
        street: `${Math.floor(Math.random() * 9999) + 1} ${
          ["Main", "Oak", "Pine", "Elm", "Maple"][Math.floor(Math.random() * 5)]
        } St`,
        city: ["Anytown", "Springfield", "Madison", "Franklin", "Georgetown"][
          Math.floor(Math.random() * 5)
        ],
        state: ["CA", "TX", "FL", "NY", "WA"][Math.floor(Math.random() * 5)],
        zipCode: String(Math.floor(Math.random() * 90000) + 10000),
        country: "US",
      },
      features: overrides.features || [
        "Hardwood floors",
        "Updated kitchen",
        "Spacious backyard",
        "Garage",
        "Central air",
      ],
      images: overrides.images || [
        {
          id: "img-1",
          url: "https://via.placeholder.com/800x600/4f46e5/ffffff?text=Property+Photo+1",
          isPrimary: true,
        },
        {
          id: "img-2",
          url: "https://via.placeholder.com/800x600/7c3aed/ffffff?text=Property+Photo+2",
          isPrimary: false,
        },
        {
          id: "img-3",
          url: "https://via.placeholder.com/800x600/059669/ffffff?text=Property+Photo+3",
          isPrimary: false,
        },
      ],
      status: overrides.status || "active",
      listingAgent: overrides.listingAgent || {
        id: userId,
        name: "John Doe",
        email: "<EMAIL>",
        phone: "+**********",
      },
      createdAt: overrides.createdAt || now,
      updatedAt: overrides.updatedAt || now,
      ...overrides,
    });

    // Auth endpoints
    if (path === "/get-session" && method === "GET") {
      return {
        session: {
          id: "session-123",
          userId,
          activeOrganizationId: workspaceId,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          createdAt: now,
          updatedAt: now,
        },
        user: {
          id: userId,
          email: "<EMAIL>",
          name: "John Doe",
          emailVerified: true,
          image: null,
          createdAt: now,
          updatedAt: now,
        },
      };
    }

    if (path.includes("/auth/sign-in") && method === "POST") {
      return {
        session: {
          id: "session-123",
          userId,
          activeOrganizationId: workspaceId,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          createdAt: now,
          updatedAt: now,
        },
        user: {
          id: userId,
          email: body?.email || "<EMAIL>",
          name: "John Doe",
          emailVerified: true,
          image: null,
          createdAt: now,
          updatedAt: now,
        },
      };
    }

    if (path.includes("/auth/sign-up") && method === "POST") {
      return {
        session: {
          id: "session-123",
          userId,
          activeOrganizationId: workspaceId,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          createdAt: now,
          updatedAt: now,
        },
        user: {
          id: userId,
          email: body?.email || "<EMAIL>",
          name: body?.name || "New User",
          emailVerified: false,
          image: null,
          createdAt: now,
          updatedAt: now,
        },
      };
    }

    if (path.includes("/auth/sign-out") && method === "POST") {
      return { success: true };
    }

    if (path.includes("/auth/email/verify") && method === "POST") {
      return {
        success: true,
        message: "Verification email sent successfully",
      };
    }

    if (path.includes("/auth/forgot-password") && method === "POST") {
      return {
        success: true,
        message: "Password reset email sent",
      };
    }

    // Profile endpoints
    if (path === "/v1/profiles/me" && method === "GET") {
      return {
        id: userId,
        workspaceId,
        role: "admin",
        firstName: "John",
        lastName: "Doe",
        displayName: "John Doe",
        email: "<EMAIL>",
        phone: "+**********",
        bio: "Experienced real estate professional with over 10 years in the industry.",
        avatar: null,
        licenseNumber: "RE123456789",
        specialties: ["Residential", "Commercial", "Investment Properties"],
        createdAt: now,
        updatedAt: now,
      };
    }

    if (path === "/v1/profiles/me" && method === "PUT") {
      return {
        success: true,
        data: {
          id: userId,
          workspaceId,
          role: "admin",
          ...body,
          updatedAt: now,
        },
      };
    }

    // Organization endpoints
    if (path === "/v1/organizations/current" && method === "GET") {
      return {
        id: workspaceId,
        name: "Acme Real Estate",
        companyName: "Acme Real Estate LLC",
        companyType: "brokerage",
        subscriptionPlan: "professional",
        status: "active",
        logoUrl: null,
        primaryColor: "#3b82f6",
        address: {
          street: "123 Business Ave",
          city: "Business City",
          state: "CA",
          zipCode: "90210",
          country: "US",
        },
        phone: "+**********",
        email: "<EMAIL>",
        website: "https://acmerealestate.com",
        licenseNumber: "BRE01234567",
        createdAt: now,
        updatedAt: now,
      };
    }

    if (path === "/v1/organizations/current" && method === "PUT") {
      return {
        id: workspaceId,
        ...body,
        updatedAt: now,
      };
    }

    if (path === "/v1/organizations/current/members" && method === "GET") {
      return [
        {
          id: userId,
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          role: "admin",
          status: "active",
          joinedAt: now,
          lastActive: now,
          user: {
            id: userId,
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe",
          },
        },
        {
          id: "user-456",
          email: "<EMAIL>",
          firstName: "Jane",
          lastName: "Smith",
          role: "agent",
          status: "active",
          joinedAt: new Date(
            Date.now() - 30 * 24 * 60 * 60 * 1000
          ).toISOString(),
          lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          user: {
            id: "user-456",
            email: "<EMAIL>",
            firstName: "Jane",
            lastName: "Smith",
          },
        },
        {
          id: "user-789",
          email: "<EMAIL>",
          firstName: "Bob",
          lastName: "Johnson",
          role: "manager",
          status: "active",
          joinedAt: new Date(
            Date.now() - 60 * 24 * 60 * 60 * 1000
          ).toISOString(),
          lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          user: {
            id: "user-789",
            email: "<EMAIL>",
            firstName: "Bob",
            lastName: "Johnson",
          },
        },
      ];
    }

    if (path === "/v1/organizations/current/members" && method === "POST") {
      return {
        success: true,
        message: "Invitation sent successfully",
        data: {
          id: `invitation-${Date.now()}`,
          email: body.email,
          role: body.role,
          status: "pending",
          createdAt: now,
        },
      };
    }

    if (
      path.includes("/v1/organizations/current/invitations") &&
      method === "GET"
    ) {
      return [
        {
          id: "invitation-123",
          email: "<EMAIL>",
          role: "agent",
          status: "pending",
          invitedBy: "John Doe",
          createdAt: new Date(
            Date.now() - 2 * 24 * 60 * 60 * 1000
          ).toISOString(),
          expiresAt: new Date(
            Date.now() + 5 * 24 * 60 * 60 * 1000
          ).toISOString(),
        },
      ];
    }

    // Listings endpoints
    if (path === "/v1/listings" && method === "GET") {
      const sampleListings = [
        generateListing("listing-123", {
          title: "Beautiful Family Home",
          description:
            "A stunning 3-bedroom home in a quiet neighborhood with modern updates throughout.",
          price: 450000,
          propertyType: "house",
          bedrooms: 3,
          bathrooms: 2,
          status: "active",
        }),
        generateListing("listing-456", {
          title: "Modern Downtown Condo",
          description: "Luxury condo with city views and premium amenities.",
          price: 650000,
          propertyType: "condo",
          bedrooms: 2,
          bathrooms: 2,
          status: "active",
        }),
        generateListing("listing-789", {
          title: "Charming Townhouse",
          description: "Spacious townhouse with private garage and patio.",
          price: 525000,
          propertyType: "townhouse",
          bedrooms: 3,
          bathrooms: 2.5,
          status: "pending",
        }),
        generateListing("listing-101", {
          title: "Investment Property",
          description: "Great rental property in up-and-coming neighborhood.",
          price: 380000,
          propertyType: "house",
          bedrooms: 2,
          bathrooms: 1,
          status: "draft",
        }),
      ];

      return {
        data: sampleListings,
        pagination: {
          page: 1,
          limit: 20,
          total: sampleListings.length,
          totalPages: 1,
        },
      };
    }

    if (path === "/v1/listings" && method === "POST") {
      return {
        data: generateListing(`listing-${Date.now()}`, {
          ...body,
          status: body.status || "draft",
        }),
      };
    }

    if (path.includes("/v1/listings/") && method === "GET") {
      const listingId = path.split("/")[3];
      return {
        data: generateListing(listingId, {
          title: "Sample Listing Details",
          description:
            "This is a detailed view of a sample property with all the information you need.",
        }),
      };
    }

    if (
      path.includes("/v1/listings/") &&
      (method === "PUT" || method === "PATCH")
    ) {
      const listingId = path.split("/")[3];
      return {
        data: generateListing(listingId, {
          ...body,
          updatedAt: now,
        }),
      };
    }

    if (path.includes("/v1/listings/") && method === "DELETE") {
      return {
        success: true,
        message: "Listing deleted successfully",
      };
    }

    if (path === "/v1/listings/draft" && method === "POST") {
      return {
        data: generateListing(`draft-${Date.now()}`, {
          ...body,
          status: "draft",
        }),
      };
    }

    if (
      path.includes("/v1/listings/") &&
      path.includes("/draft") &&
      method === "PUT"
    ) {
      const listingId = path.split("/")[3];
      return {
        data: generateListing(listingId, {
          ...body,
          status: "draft",
          updatedAt: now,
        }),
      };
    }

    if (path === "/v1/listings/bulk/csv" && method === "POST") {
      return {
        success: true,
        message: "CSV import completed successfully",
        data: {
          processed: 10,
          created: 8,
          updated: 2,
          errors: 0,
          results: Array.from({ length: 8 }, (_, i) =>
            generateListing(`bulk-${Date.now()}-${i}`, {
              title: `Imported Property ${i + 1}`,
              status: "draft",
            })
          ),
        },
      };
    }

    // File upload endpoints
    if (path === "/v1/files/upload" && method === "POST") {
      const fileTypes = [
        "image/jpeg",
        "image/png",
        "application/pdf",
        "text/csv",
      ];
      const mimeType = fileTypes[Math.floor(Math.random() * fileTypes.length)];
      const isImage = mimeType.startsWith("image/");

      return {
        data: {
          id: `file-${Date.now()}`,
          filename: `sample-file.${mimeType.split("/")[1]}`,
          originalName: `uploaded-file.${mimeType.split("/")[1]}`,
          mimeType,
          size: Math.floor(Math.random() * 5000000) + 100000, // 100KB to 5MB
          url: isImage
            ? `https://via.placeholder.com/800x600/${Math.floor(
                Math.random() * 16777215
              ).toString(16)}/ffffff?text=Uploaded+Image`
            : "#",
          publicUrl: isImage
            ? `https://via.placeholder.com/800x600/${Math.floor(
                Math.random() * 16777215
              ).toString(16)}/ffffff?text=Uploaded+Image`
            : "#",
          createdAt: now,
        },
      };
    }

    if (path.includes("/v1/files/") && method === "GET") {
      const fileId = path.split("/")[3];
      return {
        data: {
          id: fileId,
          filename: "sample-file.jpg",
          originalName: "sample-file.jpg",
          mimeType: "image/jpeg",
          size: 1024000,
          url: "https://via.placeholder.com/800x600",
          publicUrl: "https://via.placeholder.com/800x600",
          createdAt: now,
        },
      };
    }

    if (path.includes("/v1/files/") && method === "DELETE") {
      return {
        success: true,
        message: "File deleted successfully",
      };
    }

    // AI endpoints
    if (path === "/v1/ai/generate-description" && method === "POST") {
      const descriptions = [
        "This stunning property offers exceptional value and modern amenities in a prime location. Perfect for discerning buyers seeking quality and comfort.",
        "Beautiful home featuring spacious rooms, updated finishes, and excellent curb appeal. Located in a desirable neighborhood with great schools nearby.",
        "Charming property with unique character and modern conveniences. Ideal for families or professionals looking for their next home.",
        "Well-maintained residence offering comfort and style. Features include updated kitchen, spacious bedrooms, and beautiful outdoor space.",
        "Exceptional property in excellent condition. This home combines classic charm with modern updates throughout.",
      ];

      return {
        description:
          descriptions[Math.floor(Math.random() * descriptions.length)],
      };
    }

    // Health check
    if (path === "/health" && method === "GET") {
      return {
        status: "ok",
        timestamp: now,
        version: "1.0.0-placeholder",
        environment: "development",
      };
    }

    // Default fallback
    console.warn(`Placeholder API: Unhandled endpoint: ${method} ${path}`);
    return null;
  }

  private toQuery(params?: Record<string, any>): string {
    if (!params) return "";
    const search = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value === undefined || value === null || value === "") return;
      search.append(key, String(value));
    });
    const query = search.toString();
    return query ? `?${query}` : "";
  }

  // Auth
  async getSession(): Promise<SessionResponse> {
    // Endpoint aligned with SessionResponse description
    return this.request<SessionResponse>(`/get-session`, { method: "GET" });
  }

  async signInWithEmail(data: SignInRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>(API_ENDPOINTS.AUTH.SIGNIN, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async signUpWithEmail(data: SignUpRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>(API_ENDPOINTS.AUTH.SIGNUP, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async signOut(_data?: Record<string, unknown>): Promise<any> {
    // Sign out endpoint may return empty response, so handle it specially
    const response = await fetch(
      `${this.baseUrl}${API_ENDPOINTS.AUTH.SIGNOUT}`,
      {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    // For sign out, we don't need to parse the response body
    // Just check if the request was successful
    if (!response.ok) {
      const isJson = response.headers
        .get("content-type")
        ?.includes("application/json");
      const data = isJson ? await response.json() : await response.text();
      const message =
        (data as any)?.message || response.statusText || "Sign out failed";
      const error: ApiError = { message, status: response.status };
      throw error;
    }

    // Return empty object for successful sign out
    return {};
  }

  async verifyEmail(
    email: string
  ): Promise<components["schemas"]["EmailVerificationResponse"]> {
    return this.request<components["schemas"]["EmailVerificationResponse"]>(
      API_ENDPOINTS.AUTH.EMAIL_VERIFY,
      {
        method: "POST",
        body: JSON.stringify({ email }),
      }
    );
  }

  // Listings
  async getListings(
    params?: Record<string, any>
  ): Promise<ListingListResponse> {
    const query = this.toQuery(params);
    return this.request<ListingListResponse>(
      `${API_ENDPOINTS.LISTINGS.LIST}${query}`,
      { method: "GET" }
    );
  }

  async getListing(
    listingId: string,
    includeDetails = true
  ): Promise<SingleListingResponse> {
    const path = API_ENDPOINTS.LISTINGS.GET.replace(":id", listingId);
    const query = this.toQuery({ includeDetails });
    return this.request<SingleListingResponse>(`${path}${query}`, {
      method: "GET",
    });
  }

  async createListing(
    listingData: CreateListingRequest
  ): Promise<SingleListingResponse> {
    return this.request<SingleListingResponse>(API_ENDPOINTS.LISTINGS.CREATE, {
      method: "POST",
      body: JSON.stringify(listingData),
    });
  }

  async updateListing(
    listingId: string,
    listingData: UpdateListingRequest
  ): Promise<SingleListingResponse> {
    const path = API_ENDPOINTS.LISTINGS.UPDATE.replace(":id", listingId);
    return this.request<SingleListingResponse>(path, {
      method: "PUT",
      body: JSON.stringify(listingData),
    });
  }

  async deleteListing(listingId: string): Promise<DeleteResponse> {
    const path = API_ENDPOINTS.LISTINGS.DELETE.replace(":id", listingId);
    return this.request<DeleteResponse>(path, { method: "DELETE" });
  }

  async updateListingStatus(
    listingId: string,
    statusUpdate: { status: string; reason?: string; notes?: string }
  ): Promise<SingleListingResponse> {
    // Use the update endpoint with PATCH method for status updates
    const path = API_ENDPOINTS.LISTINGS.UPDATE.replace(":id", listingId);
    return this.request<SingleListingResponse>(path, {
      method: "PATCH",
      body: JSON.stringify(statusUpdate),
    });
  }

  async saveDraftListing(
    listingData: SaveDraftListingRequest
  ): Promise<SingleListingResponse> {
    // Use the dedicated draft endpoint
    return this.request<SingleListingResponse>(
      API_ENDPOINTS.LISTINGS.SAVE_DRAFT,
      {
        method: "POST",
        body: JSON.stringify(listingData),
      }
    );
  }

  async updateDraftListing(
    listingId: string,
    listingData: SaveDraftListingRequest
  ): Promise<SingleListingResponse> {
    // Use the dedicated draft update endpoint
    const path = API_ENDPOINTS.LISTINGS.UPDATE_DRAFT.replace(":id", listingId);
    return this.request<SingleListingResponse>(path, {
      method: "PUT",
      body: JSON.stringify(listingData),
    });
  }

  // Enhanced Profile Management
  async getProfile(): Promise<components["schemas"]["UserProfile"]> {
    return this.request<components["schemas"]["UserProfile"]>(
      API_ENDPOINTS.PROFILES.ME,
      {
        method: "GET",
      }
    );
  }

  async updateProfile(
    profileData: UpdateProfileRequest
  ): Promise<UpdateProfileResponse> {
    return this.request<UpdateProfileResponse>(API_ENDPOINTS.PROFILES.ME, {
      method: "PUT",
      body: JSON.stringify(profileData),
    });
  }

  // Legacy method aliases for backward compatibility
  async updateUserProfile(
    profileData: UpdateProfileRequest
  ): Promise<UpdateProfileResponse> {
    return this.updateProfile(profileData);
  }

  async forgotPassword(email: string): Promise<any> {
    return this.request<any>("/auth/forgot-password", {
      method: "POST",
      body: JSON.stringify({ email }),
    });
  }

  // Organization Management
  async getCurrentOrganization(): Promise<
    components["schemas"]["Organization"]
  > {
    return this.request<components["schemas"]["Organization"]>(
      API_ENDPOINTS.ORGANIZATIONS.CURRENT,
      {
        method: "GET",
      }
    );
  }

  async updateOrganization(
    organizationData: components["schemas"]["UpdateOrganizationRequest"]
  ): Promise<components["schemas"]["Organization"]> {
    return this.request<components["schemas"]["Organization"]>(
      API_ENDPOINTS.ORGANIZATIONS.CURRENT,
      {
        method: "PUT",
        body: JSON.stringify(organizationData),
      }
    );
  }

  // Team Member Management
  async getOrganizationMembers(): Promise<
    components["schemas"]["MembersListResponse"]
  > {
    return this.request<components["schemas"]["MembersListResponse"]>(
      API_ENDPOINTS.ORGANIZATIONS.MEMBERS,
      {
        method: "GET",
      }
    );
  }

  async inviteMember(
    inviteData: components["schemas"]["InviteMemberRequest"]
  ): Promise<components["schemas"]["SuccessResponse"]> {
    return this.request<components["schemas"]["SuccessResponse"]>(
      API_ENDPOINTS.ORGANIZATIONS.MEMBERS,
      {
        method: "POST",
        body: JSON.stringify(inviteData),
      }
    );
  }

  async removeMember(
    memberIdOrEmail: string
  ): Promise<components["schemas"]["SuccessResponse"]> {
    const path = API_ENDPOINTS.ORGANIZATIONS.MEMBER.replace(
      ":memberIdOrEmail",
      memberIdOrEmail
    );
    return this.request<components["schemas"]["SuccessResponse"]>(path, {
      method: "DELETE",
    });
  }

  async updateMemberRole(
    memberId: string,
    roleData: components["schemas"]["UpdateMemberRoleRequest"]
  ): Promise<components["schemas"]["SuccessResponse"]> {
    const path = API_ENDPOINTS.ORGANIZATIONS.MEMBER_ROLE.replace(
      ":memberId",
      memberId
    );
    return this.request<components["schemas"]["SuccessResponse"]>(path, {
      method: "PUT",
      body: JSON.stringify(roleData),
    });
  }

  // Invitation Management
  async getOrganizationInvitations(
    email?: string
  ): Promise<components["schemas"]["InvitationsListResponse"]> {
    const query = email ? this.toQuery({ email }) : "";
    return this.request<components["schemas"]["InvitationsListResponse"]>(
      `${API_ENDPOINTS.ORGANIZATIONS.INVITATIONS}${query}`,
      { method: "GET" }
    );
  }

  async acceptInvitation(
    invitationData: components["schemas"]["AcceptInvitationRequest"]
  ): Promise<components["schemas"]["SuccessResponse"]> {
    return this.request<components["schemas"]["SuccessResponse"]>(
      API_ENDPOINTS.ORGANIZATIONS.INVITATIONS_ACCEPT,
      {
        method: "POST",
        body: JSON.stringify(invitationData),
      }
    );
  }

  // Files
  async uploadFile(
    file: File,
    options: {
      fileType: "document" | "image" | "video" | "audio" | "other";
      entityType?: string;
      entityId?: string;
      isPublic?: boolean;
    }
  ): Promise<UploadFileResponse> {
    const form = new FormData();
    form.append("file", file);
    form.append("fileType", options.fileType);
    if (options.entityType) form.append("entityType", options.entityType);
    if (options.entityId) form.append("entityId", options.entityId);
    if (typeof options.isPublic === "boolean")
      form.append("isPublic", String(options.isPublic));

    const response = await fetch(
      `${this.baseUrl}${API_ENDPOINTS.FILES.UPLOAD}`,
      {
        method: "POST",
        body: form,
        credentials: "include",
      }
    );

    const data = await response.json();
    if (!response.ok) {
      const error: ApiError = {
        message: data?.message || response.statusText,
        status: response.status,
      };
      throw error;
    }
    return data as UploadFileResponse;
  }

  async getFile(fileId: string): Promise<GetFileResponse> {
    const path = API_ENDPOINTS.FILES.GET.replace(":id", fileId);
    return this.request<GetFileResponse>(path, { method: "GET" });
  }

  async deleteFile(fileId: string): Promise<DeleteResponse> {
    const path = API_ENDPOINTS.FILES.DELETE.replace(":id", fileId);
    return this.request<DeleteResponse>(path, { method: "DELETE" });
  }

  async importListingsFromCSV(file: File): Promise<BulkCreateResponse> {
    const form = new FormData();
    form.append("file", file);

    const response = await fetch(
      `${this.baseUrl}${API_ENDPOINTS.LISTINGS.BULK_CSV}`,
      {
        method: "POST",
        body: form,
        credentials: "include",
      }
    );

    const data = await response.json();
    if (!response.ok) {
      const error: ApiError = {
        message: data?.message || response.statusText,
        status: response.status,
      };
      throw error;
    }
    return data as BulkCreateResponse;
  }

  // AI Generation
  async generateBusinessDescription(data: {
    businessName: string;
    industry: string;
    location: string;
    askingPrice: string;
    cashFlow: string;
    annualRevenue?: string;
    yearEstablished?: string;
    employees?: string;
  }): Promise<{ description: string }> {
    return this.request<{ description: string }>(
      API_ENDPOINTS.AI.GENERATE_DESCRIPTION,
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  }

  // Generic helper
  async makeAuthenticatedRequest<T>(
    url: string,
    requestOptions: RequestInit = {}
  ): Promise<T> {
    const response = await fetch(
      url.startsWith("http") ? url : `${this.baseUrl}${url}`,
      {
        credentials: "include",
        ...requestOptions,
      }
    );
    const data = await response.json();
    if (!response.ok) {
      const error: ApiError = {
        message: data?.message || response.statusText,
        status: response.status,
      };
      throw error;
    }
    return data as T;
  }
}

// Better Auth client configuration
export const authClient = createAuthClient({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:3001",
  plugins: [
    magicLinkClient(), // Enable magic link authentication
  ],
  fetchOptions: {
    onError: (ctx) => {
      // Global error handling
      console.error("Auth error:", ctx.error);
    },
    onSuccess: (ctx) => {
      // Global success handling
      console.log("Auth success:", ctx.response);
    },
  },
});

// Export Better Auth hooks and methods
export const {
  useSession,
  signIn,
  signUp,
  signOut,
  magicLink, // Magic link verification methods
} = authClient;

// Export error codes for custom error handling
export const AUTH_ERROR_CODES = authClient.$ERROR_CODES;

// Export singleton instance
export const apiClient = new ApiClient();

// Helper function to check if error is an API error
export const isApiError = (error: unknown): error is ApiError => {
  return (
    typeof error === "object" &&
    error !== null &&
    "message" in error &&
    typeof (error as any).message === "string"
  );
};
