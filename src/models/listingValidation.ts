import { z } from "zod";
import { parseCurrency } from "@/lib/formatters";

// Validation schema based on API documentation
export const listingValidationSchema = z.object({
  // Required fields
  businessName: z
    .string()
    .min(1, "Business name is required")
    .max(200, "Business name cannot exceed 200 characters"),

  industry: z
    .string()
    .min(1, "Industry is required")
    .max(100, "Industry cannot exceed 100 characters"),

  askingPrice: z
    .string()
    .min(1, "Asking price is required")
    .refine((val) => {
      if (!val || val.trim() === "") return false;
      const num = parseCurrency(val);
      return num !== undefined && num > 0;
    }, "Please enter a valid asking price greater than $0 (e.g., $100,000)"),

  cashFlow: z
    .string()
    .min(1, "Cash flow/SDE is required")
    .refine((val) => {
      if (!val || val.trim() === "") return false;
      const num = parseCurrency(val);
      return num !== undefined;
    }, "Please enter a valid cash flow amount (e.g., $50,000 or -$10,000)"),

  annualRevenue: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === "") return true;
      const num = parseCurrency(val);
      return num !== undefined && num > 0;
    }, "Please enter a valid annual revenue greater than $0 (e.g., $500,000)"),

  // Status with specific enum values from API
  status: z
    .enum([
      "draft",
      "active",
      "under_contract",
      "sold",
      "confidential",
      "expired",
      "withdrawn",
    ])
    .default("active"),

  // Optional string fields
  location: z
    .string()
    .max(200, "Location must be less than 200 characters")
    .optional(),

  // Year validation
  yearEstablished: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === "") return true;
      const year = parseInt(val);
      return !isNaN(year) && year >= 1800 && year <= 2025;
    }, "Please enter a valid year between 1800 and 2025 (e.g., 1995)"),

  // Employee count validation
  employees: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === "") return true;
      const num = parseInt(val);
      return !isNaN(num) && num >= 0;
    }, "Please enter a valid number of employees (0 or greater)"),

  // Owner hours validation
  ownerHours: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === "") return true;
      const hours = parseInt(val);
      return !isNaN(hours) && hours >= 0 && hours <= 168;
    }, "Please enter valid owner hours (0-168 hours per week)"),

  // Text fields with length limits
  businessDescription: z
    .string()
    .max(5000, "Business description cannot exceed 5,000 characters")
    .optional(),

  briefDescription: z
    .string()
    .max(500, "Brief description cannot exceed 500 characters")
    .optional(),

  // Financial detail fields
  revenue2023: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === "") return true;
      const num = parseCurrency(val);
      return num !== undefined && num >= 0;
    }, "Please enter a valid 2023 revenue amount (e.g., $250,000)"),

  ebitda2023: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === "") return true;
      const num = parseCurrency(val);
      return num !== undefined;
    }, "Please enter a valid 2023 EBITDA amount (e.g., $75,000 or -$5,000)"),

  inventoryValue: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === "") return true;
      const num = parseCurrency(val);
      return num !== undefined && num >= 0;
    }, "Please enter a valid inventory value (e.g., $25,000 or $0)"),

  // Real estate status enum
  realEstateStatus: z
    .enum(["owned", "leased", "available-separately"])
    .optional(),

  customerBase: z
    .string()
    .max(1000, "Customer base description cannot exceed 1,000 characters")
    .optional(),

  // Growth and sale information
  growthOpportunities: z
    .string()
    .max(2000, "Growth opportunities cannot exceed 2,000 characters")
    .optional(),

  reasonForSale: z
    .string()
    .max(1000, "Reason for sale cannot exceed 1,000 characters")
    .optional(),

  trainingPeriod: z
    .string()
    .max(200, "Training period must be less than 200 characters")
    .optional(),

  supportType: z.enum(["on-site", "remote", "both"]).optional(),

  financingAvailable: z.boolean().default(false),

  // Additional details
  equipmentHighlights: z
    .string()
    .max(2000, "Equipment highlights must be less than 2000 characters")
    .optional(),

  supplierRelationships: z
    .string()
    .max(1000, "Supplier relationships must be less than 1000 characters")
    .optional(),

  keyEmployeeInfo: z
    .string()
    .max(1000, "Key employee info must be less than 1000 characters")
    .optional(),

  specialNotes: z
    .string()
    .max(1000, "Special notes must be less than 1000 characters")
    .optional(),
});

// Type inference for the validation schema
export type ListingValidationData = z.infer<typeof listingValidationSchema>;

// Helper function to get user-friendly field names
export const getFieldDisplayName = (fieldName: string): string => {
  const fieldNameMap: Record<string, string> = {
    businessName: "Business Name",
    industry: "Industry",
    askingPrice: "Asking Price",
    cashFlow: "Cash Flow/SDE",
    annualRevenue: "Annual Revenue",
    status: "Status",
    location: "Location",
    yearEstablished: "Year Established",
    employees: "Number of Employees",
    ownerHours: "Owner Hours per Week",
    businessDescription: "Business Description",
    briefDescription: "Brief Description",
    realEstateStatus: "Real Estate Status",
    businessModel: "Business Model",
    keyFeatures: "Key Features",
    competitiveAdvantages: "Competitive Advantages",
    customerBase: "Customer Base",
    growthOpportunities: "Growth Opportunities",
    reasonForSale: "Reason for Sale",
    trainingPeriod: "Training Period",
    supportType: "Support Type",
    financingAvailable: "Financing Available",
    equipmentHighlights: "Equipment Highlights",
    supplierRelationships: "Supplier Relationships",
    keyEmployeeInfo: "Key Employee Information",
    specialNotes: "Special Notes",
  };

  return fieldNameMap[fieldName] || fieldName;
};
