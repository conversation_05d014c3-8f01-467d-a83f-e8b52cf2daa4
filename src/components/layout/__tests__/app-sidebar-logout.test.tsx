import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AppSidebar from '../app-sidebar';

// Mock the useAuth and useWorkspace hooks
const mockNavigate = vi.fn();
const mockSignOut = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/dashboard' }),
  };
});

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    signOut: mockSignOut,
    profile: { firstName: 'Test', lastName: 'User' },
  }),
  useWorkspace: () => ({
    workspace: { companyName: 'Test Company' },
  }),
}));

describe('AppSidebar Logout Functionality', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    mockNavigate.mockClear();
    mockSignOut.mockClear();
  });

  const renderAppSidebar = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <AppSidebar />
        </BrowserRouter>
      </QueryClientProvider>
    );
  };

  it('should wait for signOut to complete before navigating', async () => {
    // Mock signOut to return a promise that resolves after a delay
    let signOutResolve: () => void;
    const signOutPromise = new Promise<void>((resolve) => {
      signOutResolve = resolve;
    });
    mockSignOut.mockReturnValue(signOutPromise);

    renderAppSidebar();

    // Find and click the logout button
    const logoutButton = screen.getByRole('button', { name: /sign out/i });
    fireEvent.click(logoutButton);

    // Verify signOut was called
    expect(mockSignOut).toHaveBeenCalledTimes(1);

    // At this point, navigation should NOT have happened yet
    expect(mockNavigate).not.toHaveBeenCalled();

    // Resolve the signOut promise
    signOutResolve!();

    // Wait for navigation to happen after signOut completes
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });

  it('should navigate even if signOut fails', async () => {
    // Mock signOut to reject
    const signOutError = new Error('Sign out failed');
    mockSignOut.mockRejectedValue(signOutError);

    // Spy on console.error to verify error logging
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    renderAppSidebar();

    // Find and click the logout button
    const logoutButton = screen.getByRole('button', { name: /sign out/i });
    fireEvent.click(logoutButton);

    // Wait for error handling and navigation
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });

    // Verify error was logged
    expect(consoleSpy).toHaveBeenCalledWith('Logout error:', signOutError);

    consoleSpy.mockRestore();
  });

  it('should call signOut with no parameters', async () => {
    mockSignOut.mockResolvedValue(undefined);

    renderAppSidebar();

    const logoutButton = screen.getByRole('button', { name: /sign out/i });
    fireEvent.click(logoutButton);

    expect(mockSignOut).toHaveBeenCalledWith();
    expect(mockSignOut).toHaveBeenCalledTimes(1);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });

  it('should use replace: true when navigating to prevent back button issues', async () => {
    mockSignOut.mockResolvedValue(undefined);

    renderAppSidebar();

    const logoutButton = screen.getByRole('button', { name: /sign out/i });
    fireEvent.click(logoutButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });
});
