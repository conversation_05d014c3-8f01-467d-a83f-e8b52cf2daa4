import React from 'react';
import { Bell, Mail, Smartphone, Users, Home, AlertTriangle, Save } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useProfileQuery, useUpdateProfilePreferencesMutation } from '@/hooks/useProfileApi';
import { cn } from '@/lib/utils';
import type { components } from '@/types/api';

interface NotificationPreferencesProps {
  className?: string;
}

export const NotificationPreferences: React.FC<NotificationPreferencesProps> = ({ className }) => {
  const { data: profile, isLoading, error } = useProfileQuery();
  const updatePreferencesMutation = useUpdateProfilePreferencesMutation();

  // Get current notification preferences or defaults
  const currentPreferences = profile?.preferences?.notifications || {
    email: {
      listingUpdates: true,
      teamUpdates: true,
      systemUpdates: true,
      weeklyDigest: false,
      marketingEmails: false,
    },
    push: {
      listingUpdates: true,
      teamUpdates: true,
      systemUpdates: true,
      mentions: true,
    },
    inApp: {
      listingUpdates: true,
      teamUpdates: true,
      systemUpdates: true,
      mentions: true,
      comments: true,
    },
  };

  const [preferences, setPreferences] = React.useState(currentPreferences);
  const [hasChanges, setHasChanges] = React.useState(false);

  // Update local state when profile loads
  React.useEffect(() => {
    if (profile?.preferences?.notifications) {
      setPreferences(profile.preferences.notifications);
    }
  }, [profile]);

  const updatePreference = (category: string, key: string, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      await updatePreferencesMutation.mutateAsync({
        notifications: preferences,
      });
      setHasChanges(false);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const notificationCategories = [
    {
      id: 'email',
      title: 'Email Notifications',
      description: 'Receive notifications via email',
      icon: Mail,
      settings: [
        {
          key: 'listingUpdates',
          label: 'Listing Updates',
          description: 'When listings are created, updated, or require attention',
        },
        {
          key: 'teamUpdates',
          label: 'Team Updates',
          description: 'When team members join, leave, or change roles',
        },
        {
          key: 'systemUpdates',
          label: 'System Updates',
          description: 'Important system announcements and maintenance notices',
        },
        {
          key: 'weeklyDigest',
          label: 'Weekly Digest',
          description: 'Summary of your week\'s activity and team updates',
        },
        {
          key: 'marketingEmails',
          label: 'Marketing Emails',
          description: 'Product updates, tips, and promotional content',
        },
      ],
    },
    {
      id: 'push',
      title: 'Push Notifications',
      description: 'Receive notifications on your devices',
      icon: Smartphone,
      settings: [
        {
          key: 'listingUpdates',
          label: 'Listing Updates',
          description: 'Immediate notifications for urgent listing changes',
        },
        {
          key: 'teamUpdates',
          label: 'Team Updates',
          description: 'When team members need your attention',
        },
        {
          key: 'systemUpdates',
          label: 'System Updates',
          description: 'Critical system alerts and maintenance',
        },
        {
          key: 'mentions',
          label: 'Mentions',
          description: 'When someone mentions you in comments or discussions',
        },
      ],
    },
    {
      id: 'inApp',
      title: 'In-App Notifications',
      description: 'Notifications within the application',
      icon: Bell,
      settings: [
        {
          key: 'listingUpdates',
          label: 'Listing Updates',
          description: 'Show notifications for listing changes in the app',
        },
        {
          key: 'teamUpdates',
          label: 'Team Updates',
          description: 'Show team-related notifications in the app',
        },
        {
          key: 'systemUpdates',
          label: 'System Updates',
          description: 'Show system announcements in the app',
        },
        {
          key: 'mentions',
          label: 'Mentions',
          description: 'Show when you\'re mentioned in the app',
        },
        {
          key: 'comments',
          label: 'Comments',
          description: 'Show notifications for new comments on your listings',
        },
      ],
    },
  ];

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent className="space-y-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-5 w-32" />
              <div className="space-y-3">
                {[1, 2, 3].map((j) => (
                  <div key={j} className="flex items-center justify-between">
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                    <Skeleton className="h-6 w-12" />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("border-destructive", className)}>
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Unable to Load Preferences</h3>
            <p className="text-muted-foreground">
              {error.message || 'An error occurred while loading notification preferences.'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Bell className="h-5 w-5" />
          <span>Notification Preferences</span>
        </CardTitle>
        <CardDescription>
          Choose how and when you want to receive notifications
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {notificationCategories.map((category, categoryIndex) => (
          <div key={category.id} className="space-y-4">
            <div className="flex items-center space-x-2">
              <category.icon className="h-5 w-5 text-muted-foreground" />
              <div>
                <h4 className="text-sm font-medium">{category.title}</h4>
                <p className="text-sm text-muted-foreground">{category.description}</p>
              </div>
            </div>

            <div className="space-y-4 ml-7">
              {category.settings.map((setting, settingIndex) => (
                <div key={setting.key}>
                  <div className="flex items-center justify-between">
                    <div className="space-y-1 flex-1">
                      <Label 
                        htmlFor={`${category.id}-${setting.key}`} 
                        className="text-sm font-medium cursor-pointer"
                      >
                        {setting.label}
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {setting.description}
                      </p>
                    </div>
                    <Switch
                      id={`${category.id}-${setting.key}`}
                      checked={preferences[category.id]?.[setting.key] || false}
                      onCheckedChange={(checked) => 
                        updatePreference(category.id, setting.key, checked)
                      }
                    />
                  </div>
                  {settingIndex < category.settings.length - 1 && (
                    <Separator className="mt-4" />
                  )}
                </div>
              ))}
            </div>

            {categoryIndex < notificationCategories.length - 1 && (
              <Separator className="mt-6" />
            )}
          </div>
        ))}

        {/* Quick Actions */}
        <div className="space-y-4 pt-4 border-t">
          <h4 className="text-sm font-medium">Quick Actions</h4>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Enable all notifications
                const allEnabled = { ...preferences };
                Object.keys(allEnabled).forEach(category => {
                  Object.keys(allEnabled[category]).forEach(key => {
                    allEnabled[category][key] = true;
                  });
                });
                setPreferences(allEnabled);
                setHasChanges(true);
              }}
            >
              Enable All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Disable all notifications except critical ones
                const criticalOnly = { ...preferences };
                Object.keys(criticalOnly).forEach(category => {
                  Object.keys(criticalOnly[category]).forEach(key => {
                    criticalOnly[category][key] = key === 'systemUpdates';
                  });
                });
                setPreferences(criticalOnly);
                setHasChanges(true);
              }}
            >
              Critical Only
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Disable all notifications
                const allDisabled = { ...preferences };
                Object.keys(allDisabled).forEach(category => {
                  Object.keys(allDisabled[category]).forEach(key => {
                    allDisabled[category][key] = false;
                  });
                });
                setPreferences(allDisabled);
                setHasChanges(true);
              }}
            >
              Disable All
            </Button>
          </div>
        </div>

        {/* Save Button */}
        {hasChanges && (
          <div className="flex justify-end pt-4 border-t">
            <Button 
              onClick={handleSave}
              disabled={updatePreferencesMutation.isPending}
            >
              {updatePreferencesMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Preferences
                </>
              )}
            </Button>
          </div>
        )}

        {/* Status Indicator */}
        {!hasChanges && !updatePreferencesMutation.isPending && (
          <div className="flex items-center justify-center pt-4 border-t">
            <Badge variant="outline" className="text-green-600 border-green-200">
              <Bell className="h-3 w-3 mr-1" />
              Preferences saved
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
