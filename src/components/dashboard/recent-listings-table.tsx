import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { formatCurrencySafe } from "@/lib/formatters";
import { Listing } from "@/types";
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface RecentListingsTableProps {
  listings: Listing[];
}

export const RecentListingsTable: React.FC<RecentListingsTableProps> = ({ listings }) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Listing Name</TableHead>
          <TableHead>Industry</TableHead>
          <TableHead>Asking Price</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right">Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {listings.map((listing) => (
          <TableRow key={listing.id}>
            <TableCell className="font-medium">{listing.businessName}</TableCell>
            <TableCell>{listing.industry}</TableCell>
            <TableCell>{formatCurrencySafe(listing.askingPrice)}</TableCell>
            <TableCell>
              <StatusBadge status={listing.status} />
            </TableCell>
            <TableCell className="text-right">
              <Button size="sm" variant="ghost" asChild>
                <Link to={`/listings/${listing.id}`}>
                  <span>View</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case 'Active':
      return <Badge variant="default">Active</Badge>;
    case 'Under Contract':
      return <Badge className="bg-amber-500">Under Contract</Badge>;
    case 'Closed':
      return <Badge variant="success">Closed</Badge>;
    case 'Archived':
      return <Badge variant="outline">Archived</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
}