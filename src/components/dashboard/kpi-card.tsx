import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { cn } from '@/lib/utils';

interface KPICardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    positive: boolean;
  };
  className?: string;
}

export const KPICard: React.FC<KPICardProps> = ({ 
  title, 
  value, 
  icon, 
  description, 
  trend,
  className 
}) => {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <h3 className="mt-2 text-3xl font-bold">{value}</h3>
            {description && (
              <p className="mt-1 text-xs text-muted-foreground">{description}</p>
            )}
            {trend && (
              <div className={cn(
                "mt-2 flex items-center text-xs",
                trend.positive ? "text-green-600" : "text-red-600"
              )}>
                <span className={cn(
                  "mr-1",
                  trend.positive ? "rotate-0" : "rotate-180"
                )}>
                  ↑
                </span>
                <span>{Math.abs(trend.value)}%</span>
                <span className="ml-1 text-muted-foreground">
                  {trend.positive ? "increase" : "decrease"}
                </span>
              </div>
            )}
          </div>
          <div className="rounded-full p-2 bg-primary/10 text-primary">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};