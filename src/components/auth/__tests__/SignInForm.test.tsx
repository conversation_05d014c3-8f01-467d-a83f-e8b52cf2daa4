import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SignInForm } from '../SignInForm';

// Mock the useAuth hook
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: vi.fn(),
}));

import { useAuth } from '@/contexts/AuthContext';

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('SignInForm Loading States', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should disable form inputs and show loading overlay during authentication', async () => {
    // Mock the useAuth hook to return initial state
    (useAuth as any).mockReturnValue({
      user: null,
      profile: null,
      workspace: null,
      loading: false,
      error: null,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      switchWorkspace: vi.fn(),
      clearError: vi.fn(),
    });

    renderWithProviders(<SignInForm />);

    // Fill in the form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });

    // Submit the form
    fireEvent.click(submitButton);

    // Check that the loading overlay appears
    await waitFor(() => {
      expect(screen.getByText('Signing you in...')).toBeInTheDocument();
    });

    // Check that form inputs are disabled
    expect(emailInput).toBeDisabled();
    expect(passwordInput).toBeDisabled();
    expect(submitButton).toBeDisabled();

    // Check that the submit button shows loading state
    expect(screen.getByText('Signing in...')).toBeInTheDocument();
  });

  it('should display error message when authentication fails', () => {
    const error = { message: 'Invalid credentials', code: 'INVALID_CREDENTIALS' };
    (useAuth as any).mockReturnValue({
      user: null,
      profile: null,
      workspace: null,
      loading: false,
      error,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      switchWorkspace: vi.fn(),
      clearError: vi.fn(),
    });

    renderWithProviders(<SignInForm />);

    // Check that error message is displayed
    expect(screen.getByText(/invalid email or password/i)).toBeInTheDocument();
  });

  it('should clear error when form is resubmitted', async () => {
    const mockClearError = vi.fn();
    (useAuth as any).mockReturnValue({
      user: null,
      profile: null,
      workspace: null,
      loading: false,
      error: null,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      switchWorkspace: vi.fn(),
      clearError: mockClearError,
    });

    renderWithProviders(<SignInForm />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    // Verify clearError was called
    expect(mockClearError).toHaveBeenCalled();
  });

  it('should not allow form submission when loading', () => {
    (useAuth as any).mockReturnValue({
      user: null,
      profile: null,
      workspace: null,
      loading: true,
      error: null,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      switchWorkspace: vi.fn(),
      clearError: vi.fn(),
    });

    renderWithProviders(<SignInForm />);

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    
    // Button should be disabled when loading
    expect(submitButton).toBeDisabled();
  });
});
