# Authentication Components

This directory contains authentication and authorization components for the multi-tenant workspace system.

## Components

### SignInForm
- Handles user authentication with workspace context
- Supports workspace selection for multi-workspace users
- Includes form validation and error handling

### SignUpForm  
- Multi-step workspace owner registration
- Creates workspace and assigns owner role
- Includes email verification integration

### ProtectedRoute
- Route-level access control based on authentication and authorization
- Supports role and permission-based protection
- <PERSON>les authentication redirects and workspace status checks
- Provides fallback components for unauthorized access

### RoleGuard
- Component-level access control for UI elements
- Conditional rendering based on roles and permissions
- Supports custom fallback components and hidden mode
- Includes useRoleGuard hook for programmatic access checks

## ProtectedRoute Usage

### Basic Authentication Protection
```tsx
// Requires user to be signed in
<ProtectedRoute>
  <Dashboard />
</ProtectedRoute>
```

### Role-Based Protection
```tsx
// Requires Admin role or above (Admin, Owner)
<ProtectedRoute requiredRole={UserRole.ADMIN}>
  <AdminPanel />
</ProtectedRoute>

// Requires exact Owner role
<ProtectedRoute requiredRole={UserRole.OWNER}>
  <BillingSettings />
</ProtectedRoute>
```

### Permission-Based Protection
```tsx
// Requires any of the specified permissions
<ProtectedRoute 
  requiredPermissions={[Permission.CREATE_LISTINGS, Permission.EDIT_LISTINGS]}
  requireAllPermissions={false}
>
  <ListingManager />
</ProtectedRoute>

// Requires all specified permissions
<ProtectedRoute 
  requiredPermissions={[Permission.MANAGE_TEAM, Permission.INVITE_MEMBERS]}
  requireAllPermissions={true}
>
  <TeamManagement />
</ProtectedRoute>
```

### Custom Fallback
```tsx
<ProtectedRoute 
  requiredRole={UserRole.ADMIN}
  fallback={<AccessDeniedPage />}
>
  <AdminContent />
</ProtectedRoute>
```

### Custom Redirect
```tsx
<ProtectedRoute redirectTo="/custom-login">
  <ProtectedContent />
</ProtectedRoute>
```

## RoleGuard Usage

### Role-Based Conditional Rendering
```tsx
// Show for specific roles
<RoleGuard allowedRoles={[UserRole.OWNER, UserRole.ADMIN]}>
  <ManageTeamButton />
</RoleGuard>

// Show for Broker and above
<RoleGuard allowedRoles={[UserRole.MEMBER, UserRole.ADMIN, UserRole.OWNER]}>
  <CreateListingButton />
</RoleGuard>
```

### Permission-Based Conditional Rendering
```tsx
// Show if user has any of the permissions
<RoleGuard 
  requiredPermissions={[Permission.EDIT_LISTINGS, Permission.DELETE_LISTINGS]}
  requireAllPermissions={false}
>
  <ListingActions />
</RoleGuard>

// Show if user has all permissions
<RoleGuard 
  requiredPermissions={[Permission.MANAGE_BILLING, Permission.VIEW_WORKSPACE_SETTINGS]}
  requireAllPermissions={true}
>
  <BillingSection />
</RoleGuard>
```

### Custom Fallback
```tsx
<RoleGuard 
  allowedRoles={[UserRole.ADMIN]}
  fallback={<div>Admin access required</div>}
>
  <AdminTools />
</RoleGuard>
```

### Hidden Mode (No Fallback)
```tsx
<RoleGuard 
  allowedRoles={[UserRole.OWNER]}
  showFallback={false}
>
  <OwnerOnlyFeature />
</RoleGuard>
```

## useRoleGuard Hook

For programmatic access control:

```tsx
import { useRoleGuard } from '@/components/auth/RoleGuard';

const MyComponent = () => {
  const { hasAccess, hasRoleAccess, hasPermissionAccess } = useRoleGuard(
    [UserRole.ADMIN], // allowed roles
    [Permission.MANAGE_TEAM], // required permissions
    true // require all permissions
  );

  if (!hasAccess) {
    return <AccessDenied />;
  }

  return <AdminContent />;
};
```

## Higher-Order Components

### withProtectedRoute
```tsx
const ProtectedAdminPanel = withProtectedRoute(AdminPanel, {
  requiredRole: UserRole.ADMIN,
  fallback: <AccessDenied />
});
```

### withRoleGuard
```tsx
const GuardedButton = withRoleGuard(Button, {
  allowedRoles: [UserRole.ADMIN, UserRole.OWNER],
  fallback: <span>Access denied</span>
});
```

## Features

### Workspace Status Handling
- Automatically handles suspended workspaces
- Checks trial expiration dates
- Shows appropriate error messages

### Role Hierarchy
- Owner > Admin > Broker > Viewer
- Higher roles inherit lower role permissions
- Admins have all broker capabilities plus management features
- Owners have all admin capabilities plus billing access

### Permission System
- Granular permission-based access control
- Support for "any" or "all" permission requirements
- Permission inheritance through role hierarchy

### Error Handling
- Graceful handling of authentication errors
- User-friendly error messages
- Loading states during permission checks

### Testing
- Comprehensive test coverage
- Mock-friendly design
- Test utilities for different scenarios

## Best Practices

1. **Use ProtectedRoute for pages/routes** that require authentication or specific roles
2. **Use RoleGuard for UI elements** that should be conditionally shown
3. **Prefer role-based access** over permission-based when possible for simplicity
4. **Use permission-based access** for fine-grained control over specific features
5. **Always provide fallbacks** for better user experience
6. **Test with different user roles** to ensure proper access control

---

# Workspace Owner Sign-Up Flow

## Overview

The sign-up flow is designed to create a comprehensive onboarding experience for new workspace owners, collecting all necessary information to set up their brokerage workspace.

## SignUpForm Component

The main multi-step registration form component that guides users through workspace creation.

**Features:**
- **Multi-step interface** with 4 distinct steps
- **Progress indicator** showing completion percentage
- **Comprehensive validation** using Zod schemas
- **Email verification integration** with Supabase Auth
- **Responsive design** with modern UI components
- **Error handling** with user-friendly messages

**Steps:**
1. **Personal Information** - Name, email, phone, license number
2. **Company Details** - Company name, type, website, address
3. **Account Security** - Password creation with strength requirements
4. **Terms & Review** - Terms acceptance and information review

**Validation Rules:**
- **Email**: Valid email format required
- **Password**: Minimum 8 characters with uppercase, lowercase, and number
- **Company Name**: Required, max 100 characters
- **Website**: Valid URL format (optional)
- **Terms**: Must be accepted to proceed

## Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant SignUpForm
    participant Supabase
    participant AuthCallback
    participant Dashboard

    User->>SignUpForm: Fill multi-step form
    SignUpForm->>SignUpForm: Validate each step
    SignUpForm->>Supabase: Sign up with email confirmation
    Supabase->>User: Send verification email
    User->>AuthCallback: Click email verification link
    AuthCallback->>Supabase: Verify email & create session
    AuthCallback->>Supabase: Create workspace & profile
    AuthCallback->>Dashboard: Redirect to dashboard
```

## Data Models

### SignUpData Interface

```typescript
interface SignUpData {
  // Personal Information
  email: string;
  password: string;
  confirmPassword: string;
  first_name: string;
  last_name: string;
  phone?: string;
  license_number?: string;
  
  // Company Information
  company_name: string;
  company_type: CompanyType;
  website?: string;
  address?: string;
  
  // Preferences
  terms_accepted: boolean;
  marketing_consent?: boolean;
}
```

## Security Considerations

- **Password Requirements**: Enforced strong password policy
- **Email Verification**: Required before workspace activation
- **Input Validation**: All inputs validated on client and server
- **CSRF Protection**: Built into Supabase Auth
- **Rate Limiting**: Handled by Supabase Auth