import React, { useState, useMemo, useEffect } from "react";
import {
  Bar<PERSON>hart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  FileText,
  Calendar,
  Download,
  Building,
  Users,
  Package,
  Eye,
  MessageSquare,
  Clock,
  Loader2,
  AlertCircle,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ReportsFilters,
  ReportsFilters as FiltersType,
} from "@/components/reports/ReportsFilters";
import { Company } from "@/components/ui/company-selector";
import { getStatusBadgeVariant, getStatusLabel } from "@/lib/status-utils";
import { useListListingsQuery } from "@/hooks/useQuery<PERSON>pi";
import { FlyingHeader } from "@/components/ui/flying-header";
import {
  getCurrentQuarter,
  quarterToDateRange,
  dateRangeToApiParams,
  calculateOverviewMetrics,
  calculateMonthlyPerformance,
  filterListingsByStatus,
  filterListingsByIndustry,
  filterListingsBySearch,
  getUniqueIndustries,
  formatCurrency,
  formatPercentage,
  validateListingData,
  isDataEmpty,
  type OverviewMetrics,
  type MonthlyPerformance,
} from "@/lib/reports-utils";
import {
  ReportsHeaderSkeleton,
  ReportsFiltersSkeleton,
  ReportsTabsSkeleton,
  ReportsOverviewSkeleton,
  ReportsListingsSkeleton,
  ReportsPerformanceSkeleton,
} from "@/components/reports/ReportsSkeleton";

interface ReportsProps {
  className?: string;
}

export const ReportsComponent: React.FC<ReportsProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [listingStatusFilter, setListingStatusFilter] = useState<string>("all");

  // Initialize filters with current quarter as default
  const [filters, setFilters] = useState<FiltersType>(() => {
    const currentQuarter = getCurrentQuarter();
    return {
      selectedCompanies: [],
      searchTerm: "",
      selectedQuarter: currentQuarter,
      selectedIndustry: undefined,
      dateRange: {
        from: currentQuarter.startDate,
        to: currentQuarter.endDate,
      },
    };
  });

  // Build API query parameters from filters
  const apiParams = useMemo(() => {
    const params: Record<string, any> = {
      limit: 1000, // Get all listings for analytics
      sortBy: 'created_at',
      sortOrder: 'desc',
    };

    // Add date filtering
    if (filters.dateRange?.from) {
      params.fromDate = filters.dateRange.from.toISOString().split('T')[0];
    }
    if (filters.dateRange?.to) {
      params.toDate = filters.dateRange.to.toISOString().split('T')[0];
    }

    return params;
  }, [filters.dateRange]);

  // Fetch listings data
  const { data: listingsResponse, isLoading, error, refetch } = useListListingsQuery(apiParams);
  const rawListings = listingsResponse?.data || [];
  const listings = validateListingData(rawListings);

  // Calculate overview metrics from API data
  const overviewMetrics: OverviewMetrics = useMemo(() => {
    return calculateOverviewMetrics(listings);
  }, [listings]);

  // Calculate monthly performance data
  const monthlyPerformance: MonthlyPerformance[] = useMemo(() => {
    return calculateMonthlyPerformance(listings);
  }, [listings]);

  // Get unique industries for filtering
  const availableIndustries = useMemo(() => {
    return getUniqueIndustries(listings);
  }, [listings]);

  // Create companies list from listings for the filter component
  const companies = useMemo(() => {
    const uniqueCompanies = new Map();
    listings.forEach(listing => {
      if (!uniqueCompanies.has(listing.id)) {
        uniqueCompanies.set(listing.id, {
          id: listing.id,
          name: listing.businessName,
          industry: listing.industry,
        });
      }
    });
    return Array.from(uniqueCompanies.values());
  }, [listings]);

  // Filter listings based on current filters
  const filteredListings = useMemo(() => {
    let filtered = [...listings];

    // Apply status filter for listings tab
    if (activeTab === 'listings') {
      filtered = filterListingsByStatus(filtered, listingStatusFilter);
    }

    // Apply company filter
    if (filters.selectedCompanies.length > 0) {
      const companyIds = filters.selectedCompanies.map((c) => c.id);
      filtered = filtered.filter(listing => companyIds.includes(listing.id));
    }

    // Apply industry filter
    if (filters.selectedIndustry) {
      filtered = filterListingsByIndustry(filtered, filters.selectedIndustry);
    }

    // Apply search filter
    if (filters.searchTerm) {
      filtered = filterListingsBySearch(filtered, filters.searchTerm);
    }

    return filtered;
  }, [listings, filters, activeTab, listingStatusFilter]);

  // Loading and error states
  if (isLoading) {
    return (
      <div className={className}>
        <div className="min-h-screen texture-bg">
          <div className="space-y-6 p-4 sm:p-6 lg:p-8">
            <ReportsHeaderSkeleton />
            <ReportsFiltersSkeleton />
            <ReportsTabsSkeleton />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <div className="min-h-screen texture-bg">
          <div className="space-y-6 p-4 sm:p-6 lg:p-8">
            <ReportsHeaderSkeleton />
            <Card variant="glass" className="shadow-xl">
              <CardContent className="p-12">
                <div className="text-center">
                  <div className="w-16 h-16 glass-card rounded-xl flex items-center justify-center mx-auto mb-4">
                    <AlertCircle className="w-8 h-8 text-red-500" />
                  </div>
                  <h3 className="text-subtitle-large font-semibold text-foreground mb-2">
                    Failed to load reports data
                  </h3>
                  <p className="text-subtitle text-muted-foreground mb-6">
                    {error?.message || 'An unexpected error occurred while loading your reports. Please try again.'}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      variant="glass-highlight"
                      onClick={() => refetch()}
                      className="flex items-center space-x-2"
                    >
                      <span>Retry</span>
                    </Button>
                    <Button
                      variant="glass"
                      onClick={() => window.location.reload()}
                      className="flex items-center space-x-2"
                    >
                      <span>Refresh Page</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="min-h-screen texture-bg">
        <FlyingHeader
          title="Reports & Analytics"
          subtitle="Analyze your business performance and track key metrics with advanced filtering."
        >
          <Button variant="glass-highlight">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </FlyingHeader>
        
        <div className="space-y-6 p-4 sm:p-6 lg:p-8 pt-0">

          {/* Advanced Filters */}
          <ReportsFilters
            filters={filters}
            onFiltersChange={setFilters}
            companies={companies}
            availableIndustries={availableIndustries}
            variant="glass"
          />

          {/* Glassmorphic Reports Tabs */}
          <Card variant="glass" className="shadow-xl">
            <CardContent className="p-6">
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="space-y-6"
              >
                <TabsList className="glass-card-subtle grid w-full grid-cols-3 p-1">
                  <TabsTrigger
                    value="overview"
                    className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
                  >
                    <BarChart3 className="h-4 w-4" />
                    <span className="hidden sm:inline">Overview</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="listings"
                    className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
                  >
                    <Building className="h-4 w-4" />
                    <span className="hidden sm:inline">Listings</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="performance"
                    className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
                  >
                    <TrendingUp className="h-4 w-4" />
                    <span className="hidden sm:inline">Performance</span>
                  </TabsTrigger>
                </TabsList>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                  {isLoading ? (
                    <ReportsOverviewSkeleton />
                  ) : isDataEmpty(listings) ? (
                    <Card variant="glass" className="shadow-xl">
                      <CardContent className="p-12">
                        <div className="text-center">
                          <div className="w-16 h-16 glass-card rounded-xl flex items-center justify-center mx-auto mb-4">
                            <BarChart3 className="w-8 h-8 text-muted-foreground" />
                          </div>
                          <h3 className="text-subtitle-large font-semibold text-foreground mb-2">
                            No data available
                          </h3>
                          <p className="text-subtitle text-muted-foreground mb-4">
                            No listings found for the selected time period. Try adjusting your filters.
                          </p>
                          <Button variant="glass-highlight" onClick={() => refetch()}>
                            Refresh Data
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <>
                      {/* Glassmorphic KPI Cards */}
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card
                      variant="glass"
                      className="shadow-xl shadow-blue-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Total Listings
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {overviewMetrics.totalListings}
                            </p>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <Building className="w-6 h-6 text-blue-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-emerald-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Active Listings
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {overviewMetrics.activeListings}
                            </p>
                            <div className="flex items-center space-x-1 text-sm">
                              <TrendingUp className="w-4 h-4 text-emerald-500" />
                              <span className="text-emerald-600 font-medium">
                                +12%
                              </span>
                            </div>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <Package className="w-6 h-6 text-emerald-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-amber-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Under Contract
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {overviewMetrics.underContract}
                            </p>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <FileText className="w-6 h-6 text-amber-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-purple-500/10"
                    >
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-2">
                            <p className="text-subtitle font-medium text-muted-foreground">
                              Closed Deals
                            </p>
                            <p className="text-2xl md:text-3xl font-bold text-foreground">
                              {overviewMetrics.closedDeals}
                            </p>
                            <div className="flex items-center space-x-1 text-sm">
                              <TrendingUp className="w-4 h-4 text-purple-500" />
                              <span className="text-purple-600 font-medium">
                                +8%
                              </span>
                            </div>
                          </div>
                          <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center">
                            <TrendingUp className="w-6 h-6 text-purple-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Additional Glassmorphic Metrics */}
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card
                      variant="glass"
                      className="shadow-xl shadow-green-500/10"
                    >
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-subtitle font-medium">
                          Total Commissions
                        </CardTitle>
                        <DollarSign className="h-4 w-4 text-green-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-foreground">
                          {formatCurrency(overviewMetrics.totalCommissions)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          +15% from last period
                        </p>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-orange-500/10"
                    >
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-subtitle font-medium">
                          Avg Days on Market
                        </CardTitle>
                        <Calendar className="h-4 w-4 text-orange-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-foreground">
                          {overviewMetrics.avgDaysOnMarket}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          -5 days from last period
                        </p>
                      </CardContent>
                    </Card>

                    <Card
                      variant="glass"
                      className="shadow-xl shadow-indigo-500/10"
                    >
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-subtitle font-medium">
                          Conversion Rate
                        </CardTitle>
                        <TrendingUp className="h-4 w-4 text-indigo-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-foreground">
                          {formatPercentage(overviewMetrics.conversionRate)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          +2.1% from last period
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                    </>
                  )}
                </TabsContent>

                {/* Listings Tab with Filtering */}
                <TabsContent value="listings" className="space-y-6">
                  {isLoading ? (
                    <ReportsListingsSkeleton />
                  ) : (
                    <Card variant="glass" className="shadow-xl">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-subtitle-large">
                            Filtered Listing Performance
                          </CardTitle>
                          <CardDescription className="text-subtitle">
                            Detailed performance metrics for your filtered
                            listings ({filteredListings.length} results).
                          </CardDescription>
                        </div>
                        <Badge variant="glass" className="text-xs">
                          {filteredListings.length} of{" "}
                          {listings.length} listings
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {/* Status Filter Tabs */}
                      <div className="mb-6">
                        <div className="flex flex-wrap gap-2">
                          {[
                            { key: 'all', label: 'All', count: listings.length },
                            { key: 'Active', label: 'Active', count: overviewMetrics.activeListings },
                            { key: 'Under Contract', label: 'Under Contract', count: overviewMetrics.underContract },
                            { key: 'Sold', label: 'Sold', count: overviewMetrics.closedDeals },
                            { key: 'Draft', label: 'Draft', count: overviewMetrics.draftListings },
                          ].map((status) => (
                            <Button
                              key={status.key}
                              variant={listingStatusFilter === status.key ? "glass-highlight" : "glass"}
                              size="sm"
                              onClick={() => setListingStatusFilter(status.key)}
                              className="flex items-center space-x-2"
                            >
                              <span>{status.label}</span>
                              <Badge variant="secondary" className="text-xs">
                                {status.count}
                              </Badge>
                            </Button>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-4">
                        {filteredListings.length === 0 ? (
                          <div className="text-center py-12">
                            <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center mx-auto mb-4">
                              <Building className="w-6 h-6 text-muted-foreground" />
                            </div>
                            <h3 className="text-subtitle-large font-semibold text-foreground mb-2">
                              No listings found
                            </h3>
                            <p className="text-subtitle text-muted-foreground">
                              Try adjusting your filters to see more results.
                            </p>
                          </div>
                        ) : (
                          filteredListings.map((listing) => (
                            <Card
                              key={listing.id}
                              variant="glass-subtle"
                              className="p-4 hover:shadow-lg transition-all duration-300"
                            >
                              <div className="flex items-center justify-between">
                                <div className="space-y-2 flex-1">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center">
                                      <Building className="w-5 h-5 text-primary" />
                                    </div>
                                    <div className="flex-1">
                                      <div className="flex items-center space-x-2">
                                        <h3 className="font-semibold text-foreground">
                                          {listing.businessName}
                                        </h3>
                                        <Badge
                                          variant={getStatusBadgeVariant(
                                            listing.status
                                          )}
                                        >
                                          {getStatusLabel(listing.status)}
                                        </Badge>
                                      </div>
                                      <p className="text-sm text-muted-foreground">
                                        {listing.industry}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-6 text-sm text-muted-foreground ml-13">
                                    <div className="flex items-center space-x-1">
                                      <Clock className="w-4 h-4" />
                                      <span>
                                        {listing.daysListed || 0} days listed
                                      </span>
                                    </div>
                                    {listing.generalLocation && (
                                      <div className="flex items-center space-x-1">
                                        <Building className="w-4 h-4" />
                                        <span>{listing.generalLocation}</span>
                                      </div>
                                    )}
                                    {listing.yearEstablished && (
                                      <div className="flex items-center space-x-1">
                                        <Calendar className="w-4 h-4" />
                                        <span>Est. {listing.yearEstablished}</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-lg font-semibold text-foreground">
                                    {listing.askingPrice ? formatCurrency(listing.askingPrice) : 'N/A'}
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    Asking Price
                                  </p>
                                </div>
                              </div>
                            </Card>
                          ))
                        )}
                      </div>
                    </CardContent>
                  </Card>
                  )}
                </TabsContent>

                {/* Performance Tab */}
                <TabsContent value="performance" className="space-y-6">
                  {isLoading ? (
                    <ReportsPerformanceSkeleton />
                  ) : (
                    <>
                      {/* Monthly Performance */}
                    <Card variant="glass" className="shadow-xl">
                    <CardHeader>
                      <CardTitle className="text-subtitle-large">
                        Monthly Performance
                      </CardTitle>
                      <CardDescription className="text-subtitle">
                        Track your monthly listing and revenue performance
                        trends.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {monthlyPerformance.length === 0 ? (
                          <div className="text-center py-12">
                            <div className="w-12 h-12 glass-card rounded-xl flex items-center justify-center mx-auto mb-4">
                              <BarChart3 className="w-6 h-6 text-muted-foreground" />
                            </div>
                            <h3 className="text-subtitle-large font-semibold text-foreground mb-2">
                              No performance data
                            </h3>
                            <p className="text-subtitle text-muted-foreground">
                              No listings found for the selected time period.
                            </p>
                          </div>
                        ) : (
                          monthlyPerformance.map((month, index) => (
                            <Card
                              key={month.month}
                              variant="glass-subtle"
                              className="p-4 hover:shadow-lg transition-all duration-300"
                            >
                              <div className="flex items-center justify-between">
                                <div className="space-y-2">
                                  <div className="flex items-center space-x-3">
                                    <div className="w-10 h-10 glass-card rounded-lg flex items-center justify-center">
                                      <Calendar className="w-5 h-5 text-primary" />
                                    </div>
                                    <div>
                                      <h3 className="font-semibold text-foreground">
                                        {month.monthName} {month.year}
                                      </h3>
                                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                        <span>{month.listings} listings</span>
                                        <span>{month.closed} closed</span>
                                        <span>
                                          {month.closed > 0
                                            ? Math.round(
                                                (month.closed /
                                                  month.listings) *
                                                  100
                                              )
                                            : 0}
                                          % conversion
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-lg font-semibold text-foreground">
                                    {formatCurrency(month.revenue)}
                                  </div>
                                  <p className="text-sm text-muted-foreground">
                                    Revenue
                                  </p>
                                  {index > 0 && monthlyPerformance[index - 1] && (
                                    <div className="flex items-center justify-end space-x-1 text-xs mt-1">
                                      {month.revenue > monthlyPerformance[index - 1].revenue ? (
                                        <>
                                          <TrendingUp className="w-3 h-3 text-emerald-500" />
                                          <span className="text-emerald-600">
                                            +
                                            {Math.round(
                                              ((month.revenue - monthlyPerformance[index - 1].revenue) /
                                                monthlyPerformance[index - 1].revenue) * 100
                                            )}
                                            %
                                          </span>
                                        </>
                                      ) : month.revenue < monthlyPerformance[index - 1].revenue ? (
                                        <>
                                          <TrendingDown className="w-3 h-3 text-red-500" />
                                          <span className="text-red-600">
                                            {Math.round(
                                              ((month.revenue - monthlyPerformance[index - 1].revenue) /
                                                monthlyPerformance[index - 1].revenue) * 100
                                            )}
                                            %
                                          </span>
                                        </>
                                      ) : (
                                        <span className="text-muted-foreground">No change</span>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </Card>
                          ))
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Summary */}
                  <Card variant="glass" className="shadow-xl">
                    <CardHeader>
                      <CardTitle className="text-subtitle-large">
                        Performance Insights
                      </CardTitle>
                      <CardDescription className="text-subtitle">
                        AI-powered insights from your performance data.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Card
                          variant="glass-subtle"
                          className="p-4 border border-emerald-500/30"
                        >
                          <div className="flex items-center space-x-2 text-emerald-700 dark:text-emerald-300">
                            <TrendingUp className="h-4 w-4" />
                            <span className="font-medium">
                              Strong Performance
                            </span>
                          </div>
                          <p className="text-sm text-emerald-600 dark:text-emerald-400 mt-1">
                            Your conversion rate has improved by 15% this
                            quarter, outperforming industry averages.
                          </p>
                        </Card>

                        <Card
                          variant="glass-subtle"
                          className="p-4 border border-blue-500/30"
                        >
                          <div className="flex items-center space-x-2 text-blue-700 dark:text-blue-300">
                            <BarChart3 className="h-4 w-4" />
                            <span className="font-medium">Market Insight</span>
                          </div>
                          <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                            Technology and food service businesses are showing
                            40% higher demand this month.
                          </p>
                        </Card>

                        <Card
                          variant="glass-subtle"
                          className="p-4 border border-purple-500/30"
                        >
                          <div className="flex items-center space-x-2 text-purple-700 dark:text-purple-300">
                            <Users className="h-4 w-4" />
                            <span className="font-medium">
                              Engagement Trend
                            </span>
                          </div>
                          <p className="text-sm text-purple-600 dark:text-purple-400 mt-1">
                            Listings with detailed descriptions receive 60% more
                            inquiries on average.
                          </p>
                        </Card>
                      </div>
                    </CardContent>
                  </Card>
                    </>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export const Reports = ReportsComponent;
