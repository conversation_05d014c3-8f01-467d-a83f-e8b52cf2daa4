import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Upload, 
  Download, 
  FileSpreadsheet,
  CheckCircle2,
  AlertTriangle,
  Info,
  File,
  Database,
  X
} from 'lucide-react';
import { useImportListingsCsvMutation } from '@/hooks/useQueryApi';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface CsvImportModal2Props {
  children: React.ReactNode;
  onSuccess?: () => void;
}

type ImportStep = 'upload' | 'results';

export default function CsvImportModal2({ children, onSuccess }: CsvImportModal2Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState<ImportStep>('upload');
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [importResults, setImportResults] = useState<any>(null);
  const [dragOver, setDragOver] = useState(false);

  const csvImportMutation = useImportListingsCsvMutation({
    showSuccessToast: false,
    onSuccess: (data) => {
      setImportResults(data);
      setCurrentStep('results');
      if (data.data.created.length > 0) {
        toast({
          title: "Import Completed",
          description: `Successfully imported ${data.data.created.length} listings`,
        });
      }
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error) => {
      toast({
        title: "Import Failed",
        description: "Failed to import CSV file. Please check the file format and try again.",
        variant: "destructive",
      });
    }
  });

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
        setCsvFile(file);
      } else {
        toast({
          title: "Invalid File",
          description: "Please select a valid CSV file.",
          variant: "destructive",
        });
      }
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
        setCsvFile(file);
      } else {
        toast({
          title: "Invalid File",
          description: "Please select a valid CSV file.",
          variant: "destructive",
        });
      }
    }
  };

  const downloadTemplate = () => {
    const a = document.createElement('a');
    a.href = '/business_listings_sample.csv';
    a.download = 'business_listings_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleImport = () => {
    if (csvFile) {
      csvImportMutation.mutate(csvFile);
    }
  };

  const resetModal = () => {
    setCsvFile(null);
    setImportResults(null);
    setCurrentStep('upload');
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] flex flex-col p-0">
        {/* Header */}
        <div className="px-6 py-5 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileSpreadsheet className="h-6 w-6 text-blue-600" />
              </div>
              {currentStep === 'upload' ? 'Import Business Listings' : 'Import Results'}
            </DialogTitle>
          </DialogHeader>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {currentStep === 'upload' && (
            <div className="space-y-6">
              {/* Quick Actions Bar */}
              <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div className="flex items-center gap-2 flex-1">
                  <Info className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    Need the correct format? Download our template first
                  </span>
                </div>
                <Button 
                  onClick={downloadTemplate} 
                  size="sm" 
                  variant="outline"
                  className="border-blue-200 text-blue-700 hover:bg-blue-100"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Template
                </Button>
              </div>

              {/* File Drop Zone */}
              <div
                className={cn(
                  "border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer",
                  dragOver 
                    ? "border-blue-500 bg-blue-50 scale-105" 
                    : csvFile 
                      ? "border-green-500 bg-green-50" 
                      : "border-gray-300 hover:border-blue-400 hover:bg-blue-50/30"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => !csvFile && document.getElementById('csv-upload')?.click()}
              >
                {csvFile ? (
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <div className="p-4 bg-green-100 rounded-full">
                        <File className="h-8 w-8 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-green-900">{csvFile.name}</h3>
                      <p className="text-green-700 mb-4">
                        {(csvFile.size / 1024).toFixed(1)} KB • Ready to import
                      </p>
                      <div className="flex gap-2 justify-center">
                        <Button 
                          onClick={(e) => {
                            e.stopPropagation();
                            setCsvFile(null);
                          }}
                          variant="outline" 
                          size="sm"
                          className="border-green-200 text-green-700 hover:bg-green-50"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                        <Button 
                          onClick={(e) => {
                            e.stopPropagation();
                            document.getElementById('csv-upload')?.click();
                          }}
                          variant="outline" 
                          size="sm"
                          className="border-green-200 text-green-700 hover:bg-green-50"
                        >
                          Change File
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <div className="p-4 bg-gray-100 rounded-full">
                        <Upload className="h-8 w-8 text-gray-400" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Drop your CSV file here</h3>
                      <p className="text-gray-600 mb-4">or click to browse your files</p>
                      <Badge variant="secondary" className="text-xs">
                        Supports CSV files up to 10MB
                      </Badge>
                    </div>
                  </div>
                )}
                
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="csv-upload"
                  disabled={csvImportMutation.isPending}
                />
              </div>

              {/* Required Fields */}
              <div className="grid md:grid-cols-2 gap-4">
                <Card className="border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <h3 className="font-semibold text-sm">Required Fields</h3>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {['Business Name', 'Industry', 'Asking Price', 'Status'].map((field) => (
                        <Badge key={field} variant="secondary" className="text-xs">
                          {field}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <Info className="h-4 w-4 text-blue-600" />
                      <h3 className="font-semibold text-sm">Format Notes</h3>
                    </div>
                    <div className="space-y-1 text-xs text-gray-600">
                      <div>• Semicolon (;) separated values</div>
                      <div>• Headers in first row</div>
                      <div>• UTF-8 encoding preferred</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {currentStep === 'results' && importResults && (
            <div className="space-y-6">
              {/* Results Summary */}
              <Card className="border-green-200 bg-green-50/50">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircle2 className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-green-900">Import Complete</h3>
                      <p className="text-green-700">Your listings have been processed</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600 mb-1">
                        {importResults.data.created.length}
                      </div>
                      <div className="text-sm text-green-700 font-medium">Successfully Imported</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-red-600 mb-1">
                        {importResults.data.failed.length}
                      </div>
                      <div className="text-sm text-red-700 font-medium">Failed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-gray-900 mb-1">
                        {importResults.data.created.length + importResults.data.failed.length}
                      </div>
                      <div className="text-sm text-gray-700 font-medium">Total Processed</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Error Details */}
              {importResults.data.failed.length > 0 && (
                <Card className="border-red-200">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      <h3 className="font-semibold text-red-900">
                        Failed Imports ({importResults.data.failed.length})
                      </h3>
                    </div>
                    <div className="max-h-48 overflow-y-auto space-y-2">
                      {importResults.data.failed.slice(0, 10).map((failure: any, index: number) => (
                        <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg text-sm">
                          <div className="font-medium text-red-900">Row {failure.index + 1}</div>
                          <div className="text-red-700">{failure.error}</div>
                        </div>
                      ))}
                      {importResults.data.failed.length > 10 && (
                        <div className="text-center py-2 text-sm text-gray-600">
                          And {importResults.data.failed.length - 10} more errors...
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 border-t bg-gray-50 flex justify-between">
          <div>
            {/* Empty div for spacing */}
          </div>
          
          <div className="flex gap-3">
            {currentStep === 'results' ? (
              <Button onClick={resetModal} size="lg" className="min-w-32">
                <CheckCircle2 className="h-4 w-4 mr-2" />
                Done
              </Button>
            ) : (
              <>
                <Button variant="outline" onClick={resetModal}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleImport}
                  disabled={!csvFile || csvImportMutation.isPending}
                  size="lg"
                  className="min-w-32"
                >
                  {csvImportMutation.isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Importing...
                    </>
                  ) : (
                    <>
                      <Database className="h-4 w-4 mr-2" />
                      Import Listings
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 