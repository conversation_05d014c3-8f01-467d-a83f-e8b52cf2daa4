import React, { useState } from "react";
import { Users, User<PERSON><PERSON>, Setting<PERSON>, BarChart3 } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { TeamOverview } from "./TeamOverview";
import { MembersList } from "./MembersList";
import { InvitationsList } from "./InvitationsList";
import { InviteMemberForm } from "./InviteMemberForm";
import { MemberRoleManager } from "./MemberRoleManager";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganizationMembersQuery } from "@/hooks/useOrganizationApi";
import { FlyingHeader } from "@/components/ui/flying-header";

interface TeamManagementProps {
  className?: string;
}

const TeamManagement: React.FC<TeamManagementProps> = ({ className }) => {
  const { user } = useAuth();
  const { data: members } = useOrganizationMembersQuery();

  const [activeTab, setActiveTab] = useState("overview");
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isRoleManagerOpen, setIsRoleManagerOpen] = useState(false);

  // Permission checks
  // Role structure:
  // - admin() plugin: admin/user (all users are admin.user until admin management is implemented)
  // - org() plugin: owner/admin/member (this is what we're managing here)
  // - On signup: users get admin.user + org.owner roles
  const currentUserMember = members?.find((m) => m.user.id === user?.id);
  const currentRole = currentUserMember?.role?.toLowerCase();
  const canManageTeam = currentRole === "owner" || currentRole === "admin";
  const canInvite = canManageTeam;
  const canManageRoles = currentRole === "owner" || currentRole === "admin";

  const handleInviteMember = () => {
    setIsInviteModalOpen(true);
  };

  const handleManageRoles = () => {
    setIsRoleManagerOpen(true);
  };

  return (
    <div className={className}>
      <FlyingHeader
        title="Team Management"
        subtitle="Manage your workspace team members, roles, and permissions"
      >
        <div className="flex space-x-2">
          {canManageRoles && (
            <Button variant="glass" size="touch" onClick={handleManageRoles}>
              <Settings className="h-4 w-4 mr-2" />
              Manage Roles
            </Button>
          )}
          {canInvite && (
            <Button variant="glass-highlight" size="touch" onClick={handleInviteMember}>
              <UserPlus className="h-4 w-4 mr-2" />
              Invite Member
            </Button>
          )}
        </div>
      </FlyingHeader>
      
      <div className="space-y-6 px-4 sm:px-6 lg:px-8">

        {/* Glassmorphic Tabs */}
        <div className="glass-card p-6 shadow-xl">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <TabsList className="glass-card-subtle grid w-full grid-cols-3 p-1">
              <TabsTrigger
                value="overview"
                className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
              >
                <BarChart3 className="h-4 w-4" />
                <span className="hidden sm:inline">Overview</span>
              </TabsTrigger>
              <TabsTrigger
                value="members"
                className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
              >
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">Members</span>
              </TabsTrigger>
              <TabsTrigger
                value="invitations"
                className="flex items-center space-x-2 data-[state=active]:glass-card data-[state=active]:shadow-lg"
              >
                <UserPlus className="h-4 w-4" />
                <span className="hidden sm:inline">Invitations</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <TeamOverview />
            </TabsContent>

            <TabsContent value="members" className="space-y-6">
              <MembersList
                onInviteMember={canInvite ? handleInviteMember : undefined}
                onManageRoles={canManageRoles ? handleManageRoles : undefined}
              />
            </TabsContent>

            <TabsContent value="invitations" className="space-y-6">
              <InvitationsList />
            </TabsContent>
          </Tabs>
        </div>

        {/* Modals */}
        <InviteMemberForm
          open={isInviteModalOpen}
          onOpenChange={setIsInviteModalOpen}
        />

        <MemberRoleManager
          open={isRoleManagerOpen}
          onOpenChange={setIsRoleManagerOpen}
        />
      </div>
    </div>
  );
};

export default TeamManagement;
