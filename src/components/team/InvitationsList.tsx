import React from "react";
import { format } from "date-fns";
import { Mail, Clock, UserX, RefreshCw } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  useOrganizationInvitationsQuery,
  useInviteMemberMutation,
} from "@/hooks/useOrganizationApi";

import { getRoleLabel } from "@/lib/role-mapping";
import type { components } from "@/types/api";

type Invitation = components["schemas"]["Invitation"];

interface InvitationsListProps {
  className?: string;
}

export const InvitationsList: React.FC<InvitationsListProps> = ({
  className,
}) => {
  const {
    data: invitations,
    isLoading,
    error,
    refetch,
  } = useOrganizationInvitationsQuery();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Pending Invitations
          </CardTitle>
          <CardDescription>Loading invitations...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Pending Invitations
          </CardTitle>
          <CardDescription className="text-destructive">
            Failed to load invitations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const pendingInvitations =
    invitations?.filter((inv) => inv.status === "pending") || [];
  const allInvitations = invitations || [];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Pending Invitations
          <Badge variant="secondary">{pendingInvitations.length}</Badge>
        </CardTitle>
        <CardDescription>
          Invitations sent to join your organization ({allInvitations.length}{" "}
          total)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {pendingInvitations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No pending invitations</p>
            <p className="text-sm">
              {allInvitations.length === 0
                ? "No invitations have been sent yet"
                : "All invitations have been accepted or expired"}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingInvitations.map((invitation) => (
              <InvitationCard key={invitation.id} invitation={invitation} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

interface InvitationCardProps {
  invitation: Invitation;
}

const InvitationCard: React.FC<InvitationCardProps> = ({ invitation }) => {
  const isExpired = new Date(invitation.expiresAt) < new Date();
  const inviteMemberMutation = useInviteMemberMutation();

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return isExpired ? "destructive" : "secondary";
      case "accepted":
        return "default";
      case "rejected":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getStatusText = (status: string) => {
    if (status.toLowerCase() === "pending" && isExpired) {
      return "Expired";
    }
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const handleResendInvitation = async () => {
    try {
      await inviteMemberMutation.mutateAsync({
        email: invitation.email,
        role: invitation.role as "admin" | "member",
        resend: true,
      });
    } catch (error) {
      // Error handling is done by the mutation hook via toast
      console.error("Failed to resend invitation:", error);
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
            <Mail className="h-5 w-5 text-muted-foreground" />
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-foreground truncate">
            {invitation.email}
          </p>
          <div className="flex items-center space-x-2 mt-1">
            <Badge variant="outline" className="text-xs">
              {getRoleLabel(invitation.role)}
            </Badge>
            <Badge
              variant={getStatusColor(invitation.status)}
              className="text-xs"
            >
              {getStatusText(invitation.status)}
            </Badge>
          </div>
          <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>
                Sent{" "}
                {format(
                  new Date(invitation.createdAt || invitation.expiresAt),
                  "MMM d, yyyy"
                )}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <UserX className="h-3 w-3" />
              <span>
                Expires {format(new Date(invitation.expiresAt), "MMM d, yyyy")}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        {(invitation.status === "pending" || isExpired) && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleResendInvitation}
            disabled={inviteMemberMutation.isPending}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${
                inviteMemberMutation.isPending ? "animate-spin" : ""
              }`}
            />
            {inviteMemberMutation.isPending ? "Sending..." : "Resend"}
          </Button>
        )}
      </div>
    </div>
  );
};
