import React, { useState } from 'react';
import { useWorkspace } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Building2, ArrowRightLeft, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useWorkspaceSwitcher } from '@/hooks/useWorkspaceSwitcher';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

interface WorkspaceSwitcherProps {
  onWorkspaceSwitch?: () => void;
}

export const WorkspaceSwitcher: React.FC<WorkspaceSwitcherProps> = ({ onWorkspaceSwitch }) => {
  const { workspace, loading: workspaceLoading, error: workspaceError } = useWorkspace();
  const { availableWorkspaces, loading, error, switchToWorkspace, isCurrentWorkspace } = useWorkspaceSwitcher();
  const navigate = useNavigate();
  
  const [selectedWorkspaceId, setSelectedWorkspaceId] = useState<string>(workspace?.id || '');

  const handleWorkspaceSwitch = async () => {
    if (!selectedWorkspaceId || isCurrentWorkspace(selectedWorkspaceId)) {
      return;
    }

    try {
      await switchToWorkspace(selectedWorkspaceId);
      
      // Call the success callback
      if (onWorkspaceSwitch) {
        onWorkspaceSwitch();
      }
      
      // Navigate to dashboard of the new workspace
      navigate('/dashboard');
    } catch (error: any) {
      // Error is already handled by the hook
      console.error('Error switching workspace:', error);
    }
  };

  const getWorkspaceStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'default';
      case 'trial':
        return 'secondary';
      case 'suspended':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getSubscriptionLabel = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'trial':
        return 'Trial';
      case 'basic':
        return 'Basic';
      case 'pro':
        return 'Pro';
      case 'enterprise':
        return 'Enterprise';
      default:
        return plan;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <LoadingOverlay
            message="Loading workspaces..."
            overlay={false}
            className="py-4"
          />
        </CardContent>
      </Card>
    );
  }

  if (!availableWorkspaces.length) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-4">
            <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Additional Workspaces</h3>
            <p className="text-muted-foreground">
              You only have access to the current workspace.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowRightLeft className="h-5 w-5" />
          Switch Workspace
        </CardTitle>
        <CardDescription>
          You have access to multiple workspaces. Switch between them here.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {workspaceError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{workspaceError.message}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-3">
          <label htmlFor="workspace-select" className="text-sm font-medium">
            Select Workspace
          </label>
          <Select value={selectedWorkspaceId} onValueChange={setSelectedWorkspaceId}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a workspace" />
            </SelectTrigger>
            <SelectContent>
              {availableWorkspaces.map((ws) => (
                <SelectItem key={ws.workspace_id} value={ws.workspace_id}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center space-x-3">
                      {ws.logo_url && (
                        <img 
                          src={ws.logo_url} 
                          alt={`${ws.company_name} logo`} 
                          className="w-6 h-6 object-contain rounded"
                        />
                      )}
                      <div>
                        <div className="font-medium">{ws.company_name}</div>
                        <div className="text-xs text-muted-foreground">
                          {ws.user_role} • {getSubscriptionLabel(ws.subscription_plan)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={getWorkspaceStatusColor(ws.status) as any}>
                        {ws.status}
                      </Badge>
                      {isCurrentWorkspace(ws.workspace_id) && (
                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                      )}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Current workspace info */}
        {workspace && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="text-sm font-medium text-muted-foreground mb-1">Current Workspace</div>
            <div className="flex items-center justify-between">
              <span className="font-medium">{workspace.company_name}</span>
              <Badge variant="outline">Current</Badge>
            </div>
          </div>
        )}

        <Button
          variant="glass-highlight"
          onClick={handleWorkspaceSwitch}
          disabled={!selectedWorkspaceId || isCurrentWorkspace(selectedWorkspaceId) || workspaceLoading || loading}
          className="w-full"
        >
          {workspaceLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Switching...
            </>
          ) : (
            <>
              <ArrowRightLeft className="mr-2 h-4 w-4" />
              Switch Workspace
            </>
          )}
        </Button>

        <div className="text-xs text-muted-foreground">
          Switching workspaces will reload the application with the selected workspace's data.
        </div>
      </CardContent>
    </Card>
  );
}; 