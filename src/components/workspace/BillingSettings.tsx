import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Check,
  Zap,
  Star,
  Crown,
  CreditCard,
  FileText,
  AlertCircle,
} from "lucide-react";

export const BillingSettings: React.FC = () => {
  // Mock data - in real app this would come from API
  const hasActiveSubscription = false;
  const currentPlan = null;
  const paymentMethods = [];
  const billingHistory = [];

  const plans = [
    {
      name: "Basic",
      price: "$29",
      period: "/month",
      description: "Perfect for individual agents or small teams",
      icon: Zap,
      color: "from-blue-500 to-cyan-500",
      features: [
        "Up to 50 listings",
        "5 team members",
        "Basic reporting",
        "Email support",
        "Standard templates",
        "10GB storage",
      ],
      recommended: false,
    },
    {
      name: "Pro",
      price: "$79",
      period: "/month",
      description: "Ideal for growing brokerage firms",
      icon: Star,
      color: "from-purple-500 to-pink-500",
      features: [
        "Up to 200 listings",
        "15 team members",
        "Advanced analytics",
        "Priority support",
        "Custom branding",
        "100GB storage",
        "Advanced workflows",
        "Client portal access",
      ],
      recommended: true,
    },
    {
      name: "Enterprise",
      price: "$199",
      period: "/month",
      description: "For large organizations with advanced needs",
      icon: Crown,
      color: "from-orange-500 to-red-500",
      features: [
        "Unlimited listings",
        "Unlimited team members",
        "Custom integrations",
        "Dedicated support",
        "White-label solution",
        "1TB storage",
        "Advanced permissions",
        "API access",
        "Custom training",
      ],
      recommended: false,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-foreground">
          Billing & Subscription
        </h2>
        <p className="text-muted-foreground">
          Manage your subscription and billing information. Choose the plan that
          fits your business needs.
        </p>
      </div>

      {/* Current Usage */}
      {hasActiveSubscription && currentPlan ? (
        <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-primary" />
              Current Plan: {currentPlan.name}
            </CardTitle>
            <CardDescription>
              Your subscription renews on {currentPlan.renewalDate}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Listings Used</p>
                <p className="text-2xl font-bold">
                  {currentPlan.usage.listings}{" "}
                  <span className="text-sm font-normal text-muted-foreground">
                    / {currentPlan.limits.listings}
                  </span>
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Team Members</p>
                <p className="text-2xl font-bold">
                  {currentPlan.usage.teamMembers}{" "}
                  <span className="text-sm font-normal text-muted-foreground">
                    / {currentPlan.limits.teamMembers}
                  </span>
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Storage Used</p>
                <p className="text-2xl font-bold">
                  {currentPlan.usage.storage}{" "}
                  <span className="text-sm font-normal text-muted-foreground">
                    / {currentPlan.limits.storage}
                  </span>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-orange-200 bg-gradient-to-r from-orange-50 to-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <AlertCircle className="h-5 w-5" />
              No Active Subscription
            </CardTitle>
            <CardDescription className="text-orange-600">
              You don't have an active subscription. Choose a plan below to get
              started.
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Pricing Plans */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {plans.map((plan) => {
          const IconComponent = plan.icon;
          return (
            <Card
              key={plan.name}
              className={`relative transition-all duration-300 hover:shadow-lg ${
                plan.recommended
                  ? "border-primary shadow-lg scale-105"
                  : currentPlan?.name === plan.name
                  ? "border-primary/50"
                  : "border-border hover:border-primary/30"
              }`}
            >
              {plan.recommended && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground px-3 py-1">
                    Recommended
                  </Badge>
                </div>
              )}
              {currentPlan?.name === plan.name && (
                <div className="absolute -top-3 right-4">
                  <Badge variant="outline" className="text-xs">
                    Current
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center space-y-4">
                <div
                  className={`mx-auto w-12 h-12 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center`}
                >
                  <IconComponent className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {plan.description}
                  </CardDescription>
                </div>
                <div className="space-y-1">
                  <div className="flex items-baseline justify-center gap-1">
                    <span className="text-3xl font-bold">{plan.price}</span>
                    <span className="text-muted-foreground">{plan.period}</span>
                  </div>
                  {currentPlan?.name === plan.name && (
                    <Badge variant="outline" className="text-xs">
                      Current Plan
                    </Badge>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <Check className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  className="w-full"
                  variant={
                    currentPlan?.name === plan.name
                      ? "outline"
                      : plan.recommended
                      ? "glass-highlight"
                      : "glass-highlight"
                  }
                  disabled={currentPlan?.name === plan.name}
                >
                  {currentPlan?.name === plan.name
                    ? "Current Plan"
                    : plan.recommended
                    ? "Get Started"
                    : "Select Plan"}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Payment Methods */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>
            Manage your payment methods and billing address
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {paymentMethods.length > 0 ? (
            <div className="space-y-3">
              {paymentMethods.map((method, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-5 bg-gradient-to-r from-blue-600 to-blue-400 rounded text-white text-xs flex items-center justify-center font-bold">
                      {method.brand.toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium">
                        •••• •••• •••• {method.last4}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Expires {method.expiry}
                      </p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No Payment Methods
              </h3>
              <p className="text-muted-foreground mb-4">
                Add a payment method to subscribe to a plan and manage your
                billing.
              </p>
            </div>
          )}

          <div className="flex gap-3">
            <Button variant="glass-highlight">Add Payment Method</Button>
            <Button variant="outline">Update Billing Address</Button>
          </div>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>
            Download invoices and view payment history
          </CardDescription>
        </CardHeader>
        <CardContent>
          {billingHistory.length > 0 ? (
            <div className="space-y-3">
              {billingHistory.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <div>
                      <p className="font-medium">{item.date}</p>
                      <p className="text-sm text-muted-foreground">
                        {item.invoice}
                      </p>
                    </div>
                    <Badge
                      variant="outline"
                      className="text-green-600 border-green-200"
                    >
                      {item.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="font-medium">{item.amount}</span>
                    <Button variant="ghost" size="sm">
                      Download
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No Billing History
              </h3>
              <p className="text-muted-foreground">
                Your billing history and invoices will appear here once you have
                an active subscription.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
