import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        success:
          "border-transparent bg-green-600 text-white hover:bg-green-700",
        accent:
          "border-transparent bg-accent text-accent-foreground hover:bg-accent/80",
        active:
          "border-transparent bg-primary/90 text-primary-foreground border border-primary",
        inactive:
          "border-transparent bg-muted text-muted-foreground",

        // Glassmorphic Status Variants - Optimized
        "status-active":
          "status-active-glass shadow-sm hover:shadow-md",
        "status-under-contract":
          "status-under-contract-glass shadow-sm hover:shadow-md",
        "status-sold":
          "status-sold-glass shadow-sm hover:shadow-md",
        "status-draft":
          "status-draft-glass shadow-sm hover:shadow-md",
        "status-confidential":
          "status-confidential-glass shadow-sm hover:shadow-md",
        "status-archived":
          "status-archived-glass shadow-sm hover:shadow-md",

        // Solid Status Variants (for high contrast needs)
        "solid-active":
          "status-active shadow-md hover:shadow-lg",
        "solid-under-contract":
          "status-under-contract shadow-md hover:shadow-lg",
        "solid-sold":
          "status-sold shadow-md hover:shadow-lg",
        "solid-draft":
          "status-draft shadow-md hover:shadow-lg",
        "solid-confidential":
          "status-confidential shadow-md hover:shadow-lg",
        "solid-archived":
          "status-archived shadow-md hover:shadow-lg",

        // Glass variants for general use
        glass:
          "glass-card-subtle text-foreground hover:bg-white/10 dark:hover:bg-black/20",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
