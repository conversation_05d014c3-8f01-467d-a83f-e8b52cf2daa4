import * as React from "react"
import { Calendar, Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface Quarter {
  id: string
  label: string
  year: number
  quarter: number
  startDate: Date
  endDate: Date
}

interface QuarterSelectorProps {
  selectedQuarter?: Quarter
  onQuarterChange: (quarter: Quarter | undefined) => void
  placeholder?: string
  variant?: "default" | "glass"
  className?: string
  yearsBack?: number
}

// Generate quarters for the last few years
function generateQuarters(yearsBack: number = 3): Quarter[] {
  const quarters: Quarter[] = []
  const currentYear = new Date().getFullYear()
  
  for (let year = currentYear; year >= currentYear - yearsBack; year--) {
    for (let q = 4; q >= 1; q--) {
      const startMonth = (q - 1) * 3
      const endMonth = startMonth + 2
      
      quarters.push({
        id: `${year}-Q${q}`,
        label: `Q${q} ${year}`,
        year,
        quarter: q,
        startDate: new Date(year, startMonth, 1),
        endDate: new Date(year, endMonth + 1, 0), // Last day of the quarter
      })
    }
  }
  
  return quarters
}

export function QuarterSelector({
  selectedQuarter,
  onQuarterChange,
  placeholder = "Select quarter...",
  variant = "default",
  className,
  yearsBack = 3,
}: QuarterSelectorProps) {
  const [open, setOpen] = React.useState(false)
  const quarters = React.useMemo(() => generateQuarters(yearsBack), [yearsBack])

  const handleSelect = (quarter: Quarter) => {
    onQuarterChange(selectedQuarter?.id === quarter.id ? undefined : quarter)
    setOpen(false)
  }

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={variant === "glass" ? "glass" : "outline"}
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              variant === "glass" && "glass-input"
            )}
          >
            <div className="flex items-center">
              <Calendar className="mr-2 h-4 w-4" />
              {selectedQuarter ? selectedQuarter.label : placeholder}
            </div>
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          className={cn(
            "w-full p-0",
            variant === "glass" && "glass-card"
          )}
        >
          <Command>
            <CommandInput placeholder="Search quarters..." />
            <CommandList>
              <CommandEmpty>No quarters found.</CommandEmpty>
              <CommandGroup>
                {quarters.map((quarter) => (
                  <CommandItem
                    key={quarter.id}
                    value={quarter.label}
                    onSelect={() => handleSelect(quarter)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedQuarter?.id === quarter.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex flex-col">
                      <span>{quarter.label}</span>
                      <span className="text-xs text-muted-foreground">
                        {quarter.startDate.toLocaleDateString()} - {quarter.endDate.toLocaleDateString()}
                      </span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}

// Helper function to get companies added during a specific quarter
export function getCompaniesForQuarter(
  companies: Array<{ id: string; name: string; createdAt: Date }>,
  quarter: Quarter
): Array<{ id: string; name: string }> {
  return companies
    .filter(company => 
      company.createdAt >= quarter.startDate && 
      company.createdAt <= quarter.endDate
    )
    .map(({ id, name }) => ({ id, name }))
}
