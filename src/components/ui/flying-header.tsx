import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface FlyingHeaderProps {
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
  bottomContent?: React.ReactNode;
  className?: string;
}

export const FlyingHeader: React.FC<FlyingHeaderProps> = ({
  title,
  subtitle,
  children,
  bottomContent,
  className
}) => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className={cn(
      "sticky top-0 z-50 mb-6 transition-all duration-500 ease-out",
      // Base glassmorphic styling with rounded corners
      "bg-white/70 dark:bg-gray-900/70 backdrop-blur-2xl",
      "border border-white/30 dark:border-gray-800/30",
      "rounded-b-2xl mx-2 sm:mx-4 lg:mx-6",
      // Enhanced shadow and blur when scrolled
      isScrolled 
        ? "shadow-2xl shadow-black/10 dark:shadow-black/30 bg-white/85 dark:bg-gray-900/85 backdrop-blur-3xl rounded-b-3xl" 
        : "shadow-lg shadow-black/5 dark:shadow-black/20",
      // Subtle gradient overlay
      "before:absolute before:inset-0 before:bg-gradient-to-r before:from-blue-500/5 before:via-purple-500/5 before:to-pink-500/5 before:pointer-events-none before:rounded-b-2xl",
      isScrolled && "before:rounded-b-3xl",
      className
    )}>
      <div className="relative px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-1">
            <h1 className={cn(
              "font-bold tracking-tight transition-all duration-300",
              "bg-gradient-to-r from-gray-900 via-blue-900 to-gray-700 dark:from-white dark:via-blue-100 dark:to-gray-200 bg-clip-text text-transparent",
              isScrolled ? "text-2xl sm:text-2xl" : "text-2xl sm:text-3xl"
            )}>
              {title}
            </h1>
            {subtitle && (
              <p className={cn(
                "text-muted-foreground transition-all duration-300",
                isScrolled ? "text-xs sm:text-sm opacity-80" : "text-sm sm:text-base"
              )}>
                {subtitle}
              </p>
            )}
          </div>
          {children && (
            <div className={cn(
              "flex-shrink-0 transition-all duration-300",
              isScrolled ? "scale-95" : "scale-100"
            )}>
              {children}
            </div>
          )}
        </div>
        
        {/* Bottom content section */}
        {bottomContent && (
          <div className={cn(
            "mt-6 pt-4 border-t border-white/20 dark:border-gray-800/20 transition-all duration-300",
            isScrolled ? "mt-4 pt-3" : "mt-6 pt-4"
          )}>
            {bottomContent}
          </div>
        )}
      </div>
      
      {/* Subtle animated border with rounded corners */}
      <div className={cn(
        "absolute bottom-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent rounded-full"
      )} />
    </div>
  );
};