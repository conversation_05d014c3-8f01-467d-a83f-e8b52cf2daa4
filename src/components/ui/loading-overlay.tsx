import React, { useEffect } from 'react';
import { cn } from '@/lib/utils';

interface LoadingOverlayProps {
  message?: string;
  className?: string;
  showMessage?: boolean;
  overlay?: boolean; // Whether to show as full-screen overlay
  source?: string; // Source identifier for debugging
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  message = '',
  className,
  showMessage = true,
  overlay = true,
  source = 'Unknown',
}) => {
  // Log loading overlay usage for debugging
  useEffect(() => {
    const timestamp = new Date().toISOString();
    const overlayType = overlay ? 'fullscreen' : 'inline';
    const currentPath = window.location.pathname;

    console.log(`LoadingOverlay [${overlayType}] rendered:`, {
      source,
      message,
      overlayType,
      currentPath,
      timestamp,
      showMessage,
    });

    // Return cleanup function to log when overlay is removed
    return () => {
      console.log(`LoadingOverlay [${overlayType}] removed:`, {
        source,
        currentPath,
        timestamp: new Date().toISOString(),
      });
    };
  }, [source, message, overlay, showMessage]);
  const content = (
    <div className="flex flex-col items-center justify-center text-center">
      {/* Centralized spinner with exact styling requested */}
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      
      {/* Optional message */}
      {showMessage && (
        <p className="text-muted-foreground text-sm">
          {message}
        </p>
      )}
    </div>
  );

  if (overlay) {
    // Full-screen overlay with glassmorphism
    return (
      <div className={cn(
        "fixed inset-0 z-50 flex items-center justify-center",
        "bg-background/80 backdrop-blur-sm",
        "border border-border/20",
        "transition-all duration-200",
        className
      )}>
        <div className={cn(
          "rounded-lg border border-border/50 bg-background/30 backdrop-blur-md",
          "shadow-lg p-8 mx-4",
          "transition-all duration-200"
        )}>
          {content}
        </div>
      </div>
    );
  }

  // Inline loading with glassmorphism
  return (
    <div className={cn(
      "flex items-center justify-center p-8",
      "rounded-lg border border-border/50 bg-background/30 backdrop-blur-md",
      "shadow-sm",
      className
    )}>
      {content}
    </div>
  );
};

// Convenience component for button loading states
export const ButtonSpinner: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn(
      "animate-spin rounded-full h-4 w-4 border-b-2 border-current",
      className
    )} />
  );
};
