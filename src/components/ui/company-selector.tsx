import * as React from "react"
import { Check, ChevronsUpDown, X } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

export interface Company {
  id: string
  name: string
  industry?: string
}

interface CompanySelectorProps {
  companies: Company[]
  selectedCompanies: Company[]
  onSelectionChange: (companies: Company[]) => void
  placeholder?: string
  variant?: "default" | "glass"
  className?: string
  maxDisplayed?: number
}

export function CompanySelector({
  companies,
  selectedCompanies,
  onSelectionChange,
  placeholder = "Select companies...",
  variant = "default",
  className,
  maxDisplayed = 3,
}: CompanySelectorProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (company: Company) => {
    const isSelected = selectedCompanies.some(c => c.id === company.id)
    if (isSelected) {
      onSelectionChange(selectedCompanies.filter(c => c.id !== company.id))
    } else {
      onSelectionChange([...selectedCompanies, company])
    }
  }

  const handleRemove = (companyId: string) => {
    onSelectionChange(selectedCompanies.filter(c => c.id !== companyId))
  }

  const displayedCompanies = selectedCompanies.slice(0, maxDisplayed)
  const remainingCount = selectedCompanies.length - maxDisplayed

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={variant === "glass" ? "glass" : "outline"}
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between min-h-10 h-auto",
              variant === "glass" && "glass-input"
            )}
          >
            <div className="flex flex-wrap gap-1 flex-1">
              {selectedCompanies.length === 0 ? (
                <span className="text-muted-foreground">{placeholder}</span>
              ) : (
                <>
                  {displayedCompanies.map((company) => (
                    <Badge
                      key={company.id}
                      variant={variant === "glass" ? "glass" : "secondary"}
                      className="text-xs"
                    >
                      {company.name}
                      <button
                        className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            handleRemove(company.id)
                          }
                        }}
                        onMouseDown={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                        }}
                        onClick={() => handleRemove(company.id)}
                      >
                        <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                      </button>
                    </Badge>
                  ))}
                  {remainingCount > 0 && (
                    <Badge variant="outline" className="text-xs">
                      +{remainingCount} more
                    </Badge>
                  )}
                </>
              )}
            </div>
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          className={cn(
            "w-full p-0",
            variant === "glass" && "glass-card"
          )}
        >
          <Command>
            <CommandInput placeholder="Search companies..." />
            <CommandList>
              <CommandEmpty>No companies found.</CommandEmpty>
              <CommandGroup>
                {companies.map((company) => (
                  <CommandItem
                    key={company.id}
                    value={company.name}
                    onSelect={() => handleSelect(company)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedCompanies.some(c => c.id === company.id)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    <div className="flex flex-col">
                      <span>{company.name}</span>
                      {company.industry && (
                        <span className="text-xs text-muted-foreground">
                          {company.industry}
                        </span>
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
