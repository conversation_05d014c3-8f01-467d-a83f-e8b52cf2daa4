import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-150 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent ",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        accent: "bg-accent text-accent-foreground hover:bg-accent/90",
        success: "bg-green-600 text-white hover:bg-green-700",
        active: "bg-primary/90 text-primary-foreground border border-primary",
        inactive: "bg-muted text-muted-foreground hover:bg-muted/80",

        // Glassmorphic variants
        glass: "glass-button text-foreground hover:text-foreground",
        "glass-primary": "glass-button text-primary hover:text-primary border-primary/20 hover:border-primary/30",
        "glass-accent": "glass-button text-accent hover:text-accent border-accent/20 hover:border-accent/30",
        "glass-highlight": "shadow-xl shadow-blue-500/25 border-blue-500/30 bg-blue-500/10 hover:bg-blue-500/20 hover:shadow-2xl hover:shadow-blue-500/30 transition-all duration-300 font-semibold text-blue-700 dark:text-blue-300 ring-1 ring-blue-500/20 hover:ring-blue-500/40 backdrop-blur-md border rounded-lg",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
        "touch": "h-12 px-6 py-3", // Touch-friendly size for mobile
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
