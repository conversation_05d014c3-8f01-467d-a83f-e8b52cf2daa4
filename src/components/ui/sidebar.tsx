import * as React from "react"
import { cn } from "@/lib/utils"
import { cva } from "class-variance-authority"
import { ChevronDown } from "lucide-react"
import { buttonVariants } from "./button"

/**
 * Sidebar Context & Provider
 */
type SidebarContextValue = {
  collapsed: boolean
  setCollapsed: (collapsed: boolean) => void
  toggleCollapsed: () => void
  collapsedWidth: number
}

const SidebarContext = React.createContext<SidebarContextValue | undefined>(
  undefined
)

export interface SidebarProviderProps {
  children: React.ReactNode
  defaultCollapsed?: boolean
  collapsedWidth?: number
}

export function SidebarProvider({
  children,
  defaultCollapsed = false,
  collapsedWidth = 56,
}: SidebarProviderProps) {
  const [collapsed, setCollapsed] = React.useState(defaultCollapsed)

  const toggleCollapsed = () => setCollapsed((prev) => !prev)

  return (
    <SidebarContext.Provider
      value={{ collapsed, setCollapsed, toggleCollapsed, collapsedWidth }}
    >
      {children}
    </SidebarContext.Provider>
  )
}

/**
 * Hook to consume the sidebar context
 */
export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider")
  }
  return context
}

/**
 * Component Variants
 */
const menuItemVariants = cva(
  "flex cursor-pointer items-center gap-2 rounded-md px-3 py-2 transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:outline-none focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-sidebar-ring",
  {
    variants: {
      active: {
        true: "bg-sidebar-accent text-sidebar-accent-foreground",
        false: "text-sidebar-foreground",
      },
      disabled: {
        true: "cursor-not-allowed opacity-60",
        false: "",
      },
    },
    defaultVariants: {
      active: false,
      disabled: false,
    },
  }
)

const sidebarItemVariants = cva("", {
  variants: {
    active: {
      true: "font-medium",
      false: "",
    },
    size: {
      sm: "text-sm",
      md: "text-base",
      lg: "text-lg",
    },
  },
  defaultVariants: {
    active: false,
    size: "md",
  },
})

/**
 * Component Type Definitions
 */
export interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  collapsible?: boolean
}

export interface SidebarGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  open?: boolean
  defaultOpen?: boolean
  onOpenChange?: (open: boolean) => void
  label?: string
  icon?: React.ReactNode
  children?: React.ReactNode
}

export interface SidebarMenuItemProps
  extends React.HTMLAttributes<HTMLDivElement> {
  active?: boolean
  disabled?: boolean
}

/**
 * Sidebar Components
 */
export function Sidebar({
  className,
  children,
  collapsible,
  ...props
}: SidebarProps) {
  const { collapsed } = useSidebar()

  return (
    <div
      className={cn(
        "flex h-screen flex-col border-r border-sidebar-border bg-sidebar text-sidebar-foreground",
        className
      )}
      data-collapsed={collapsed}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarHeader({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "flex h-14 items-center border-b border-sidebar-border px-3 py-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarContent({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("flex-1 overflow-auto p-2", className)}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarFooter({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "flex items-center border-t border-sidebar-border p-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarGroup({
  className,
  children,
  open: openProp,
  defaultOpen = false,
  onOpenChange,
  label,
  icon,
  ...props
}: SidebarGroupProps) {
  const [open, setOpen] = React.useState(defaultOpen)
  const { collapsed } = useSidebar()

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    onOpenChange?.(newOpen)
  }

  const isOpen = openProp !== undefined ? openProp : open

  return (
    <div
      className={cn("mb-2 space-y-1", className)}
      data-state={isOpen ? "open" : "closed"}
      {...props}
    >
      {label && (
        <button
          className="flex w-full cursor-pointer items-center justify-between rounded-md px-3 py-2 font-medium text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground"
          onClick={() => handleOpenChange(!isOpen)}
        >
          <span className="flex items-center gap-2">
            {icon}
            {!collapsed && <span>{label}</span>}
          </span>
          {!collapsed && (
            <ChevronDown
              className="h-4 w-4 transition-transform"
              style={{ transform: isOpen ? "rotate(0deg)" : "rotate(-90deg)" }}
            />
          )}
        </button>
      )}
      {(isOpen || collapsed) && children}
    </div>
  )
}

export function SidebarGroupContent({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const { collapsed } = useSidebar()

  return (
    <div
      className={cn(
        "space-y-1 pl-0",
        !collapsed && "pl-3",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarGroupLabel({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const { collapsed } = useSidebar()

  if (collapsed) {
    return null
  }

  return (
    <div
      className={cn(
        "px-3 py-1 text-xs uppercase tracking-wider text-sidebar-foreground/60",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarItem({
  className,
  children,
  active = false,
  size = "md",
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  active?: boolean
  size?: "sm" | "md" | "lg"
}) {
  return (
    <div
      className={cn(sidebarItemVariants({ active, size }), className)}
      data-active={active}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarMenu({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("space-y-1", className)}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarMenuItem({
  className,
  children,
  active,
  disabled,
  ...props
}: SidebarMenuItemProps) {
  return (
    <div
      className={cn(
        menuItemVariants({ active, disabled }),
        className
      )}
      data-active={active}
      data-disabled={disabled}
      {...props}
    >
      {children}
    </div>
  )
}

export function SidebarMenuButton({
  className,
  children,
  asChild = false,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & {
  asChild?: boolean
}) {
  if (asChild) {
    return React.cloneElement(
      React.Children.only(children as React.ReactElement),
      {
        className: cn(
          "flex w-full cursor-pointer items-center gap-2 rounded-md px-3 py-2 transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:outline-none focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-sidebar-ring",
          className
        ),
        ...props,
      }
    )
  }

  return (
    <button
      className={cn(
        "flex w-full cursor-pointer items-center gap-2 rounded-md px-3 py-2 transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:outline-none focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-sidebar-ring",
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
}

export function SidebarTrigger({
  className,
  ...props
}: React.HTMLAttributes<HTMLButtonElement>) {
  const { toggleCollapsed } = useSidebar()

  return (
    <button
      onClick={toggleCollapsed}
      className={cn(
        buttonVariants({ variant: "ghost", size: "sm" }),
        "h-9 w-9 p-0",
        className
      )}
      {...props}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
        <line x1="9" y1="3" x2="9" y2="21" />
      </svg>
      <span className="sr-only">Toggle Sidebar</span>
    </button>
  )
}