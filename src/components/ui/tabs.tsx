import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";

import { cn } from "@/lib/utils";

const Tabs = TabsPrimitive.Root;

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex items-center justify-center rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 shadow-lg p-1.5 text-slate-600 dark:text-slate-300 relative overflow-hidden",
      // Enhanced glass effect with multiple layers
      "before:absolute before:inset-0 before:bg-gradient-to-r before:from-white/8 before:via-white/4 before:to-transparent before:pointer-events-none before:rounded-2xl",
      "after:absolute after:inset-0 after:bg-gradient-to-b after:from-transparent after:via-white/2 after:to-white/5 after:pointer-events-none after:rounded-2xl",
      // Subtle inner shadow for depth
      "shadow-[inset_0_1px_0_0_rgba(255,255,255,0.1),inset_0_-1px_0_0_rgba(0,0,0,0.05)]",
      className
    )}
    {...props}
  />
));
TabsList.displayName = TabsPrimitive.List.displayName;

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-xl px-5 py-2.5 text-sm font-medium transition-all duration-300 ease-out relative group",
      // Base state with subtle glass effect
      "text-slate-600 dark:text-slate-400",
      "hover:text-slate-800 dark:hover:text-slate-200 hover:bg-white/8 hover:backdrop-blur-sm",
      "hover:shadow-[0_2px_8px_rgba(0,0,0,0.08)] hover:scale-[1.02]",
      // Focus states
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/40 focus-visible:ring-offset-2 focus-visible:ring-offset-transparent",
      "disabled:pointer-events-none disabled:opacity-50",
      // Active state with enhanced glass morphism
      "data-[state=active]:text-slate-900 dark:data-[state=active]:text-white data-[state=active]:font-semibold",
      "data-[state=active]:bg-white/25 data-[state=active]:backdrop-blur-lg",
      "data-[state=active]:shadow-[0_4px_16px_rgba(0,0,0,0.12),inset_0_1px_0_rgba(255,255,255,0.2)]",
      "data-[state=active]:border data-[state=active]:border-white/30",
      // Active state gradient overlay
      "data-[state=active]:before:absolute data-[state=active]:before:inset-0 data-[state=active]:before:bg-gradient-to-br data-[state=active]:before:from-white/20 data-[state=active]:before:via-white/10 data-[state=active]:before:to-transparent data-[state=active]:before:rounded-xl data-[state=active]:before:-z-10",
      // Subtle shimmer effect on hover
      "overflow-hidden",
      "hover:before:absolute hover:before:inset-0 hover:before:bg-gradient-to-r hover:before:from-transparent hover:before:via-white/10 hover:before:to-transparent hover:before:translate-x-[-100%] hover:before:animate-[shimmer_1.5s_ease-out] hover:before:rounded-xl",
      className
    )}
    {...props}
  />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-6 rounded-2xl bg-white/8 backdrop-blur-md border border-white/15 shadow-lg p-8 relative overflow-hidden",
      // Enhanced glass effect with multiple gradient layers
      "before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/12 before:via-transparent before:to-white/6 before:pointer-events-none before:rounded-2xl",
      "after:absolute after:inset-0 after:bg-gradient-to-t after:from-white/5 after:via-transparent after:to-white/8 after:pointer-events-none after:rounded-2xl",
      // Inner shadow for depth
      "shadow-[inset_0_1px_0_0_rgba(255,255,255,0.1),0_8px_32px_rgba(0,0,0,0.08)]",
      // Focus states
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/40 focus-visible:ring-offset-2 focus-visible:ring-offset-transparent",
      // Enhanced entrance animation
      "animate-in fade-in-0 slide-in-from-bottom-2 duration-400 ease-out",
      // Subtle texture overlay
      "bg-[radial-gradient(circle_at_50%_0%,rgba(255,255,255,0.1),transparent_50%)]",
      className
    )}
    {...props}
  />
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent };
