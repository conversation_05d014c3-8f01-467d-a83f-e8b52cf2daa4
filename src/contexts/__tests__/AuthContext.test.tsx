import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider, useAuth } from '../AuthContext';

// Mock the useAuth API hooks
vi.mock('@/hooks/useAuthApi', () => ({
  useSessionQuery: vi.fn(),
  useSignInMutation: vi.fn(),
  useSignUpMutation: vi.fn(),
}));

// Mock the API client
vi.mock('@/lib/api-client', () => ({
  apiClient: {
    signOut: vi.fn(),
  },
  isApiError: vi.fn(),
}));

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/dashboard' }),
  };
});

// Test component to access auth context
const TestComponent = () => {
  const { user, loading } = useAuth();
  return (
    <div>
      <div data-testid="user">{user ? 'authenticated' : 'not-authenticated'}</div>
      <div data-testid="loading">{loading ? 'loading' : 'not-loading'}</div>
    </div>
  );
};

describe('AuthContext Session Monitoring', () => {
  let queryClient: QueryClient;
  const mockUseSessionQuery = vi.fn();
  const mockUseSignInMutation = vi.fn();
  const mockUseSignUpMutation = vi.fn();

  beforeEach(async () => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    mockNavigate.mockClear();

    // Setup default mocks
    mockUseSessionQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    });

    mockUseSignInMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isPending: false,
    });

    mockUseSignUpMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isPending: false,
    });

    // Apply mocks
    const { useSessionQuery, useSignInMutation, useSignUpMutation } = await import('@/hooks/useAuthApi');
    vi.mocked(useSessionQuery).mockImplementation(mockUseSessionQuery);
    vi.mocked(useSignInMutation).mockImplementation(mockUseSignInMutation);
    vi.mocked(useSignUpMutation).mockImplementation(mockUseSignUpMutation);
  });

  const renderAuthProvider = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        </BrowserRouter>
      </QueryClientProvider>
    );
  };

  it('should render without errors when session monitoring is integrated', () => {
    const { getByTestId } = renderAuthProvider();
    
    expect(getByTestId('user')).toHaveTextContent('not-authenticated');
    expect(getByTestId('loading')).toHaveTextContent('not-loading');
  });

  it('should handle session loss and trigger navigation', async () => {
    // First render with authenticated user
    mockUseSessionQuery.mockReturnValue({
      data: { user: { id: '1', email: '<EMAIL>' } },
      isLoading: false,
      error: null,
    });

    const { rerender, getByTestId } = renderAuthProvider();
    
    expect(getByTestId('user')).toHaveTextContent('authenticated');

    // Then simulate session loss
    mockUseSessionQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    });

    rerender(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <AuthProvider>
            <TestComponent />
          </AuthProvider>
        </BrowserRouter>
      </QueryClientProvider>
    );

    await waitFor(() => {
      expect(getByTestId('user')).toHaveTextContent('not-authenticated');
    });

    // Should trigger navigation to home page
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });
});
