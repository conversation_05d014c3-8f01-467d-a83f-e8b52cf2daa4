import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/hooks/use-toast';
import {
  useCreateListingMutation,
  useUpdateListingMutation,
  useDeleteListingMutation,
  useSaveDraftListingMutation,
  useUpdateDraftListingMutation,
  useImportListingsCsvMutation,
  useListings
} from '@/hooks/useQueryApi';
import type { CreateListingRequest, SaveDraftListingRequest } from '@/lib/api-client';

export default function TestListingCrud() {
  const [testData, setTestData] = useState({
    businessName: 'Test Business',
    industry: 'restaurant',
    askingPrice: 100000,
    cashFlowSde: 50000,
    annualRevenue: 200000,
    status: 'draft',
    generalLocation: 'Test City',
    yearEstablished: 2020,
    employees: 5,
    ownerHoursWeek: 40,
  });

  const [selectedListingId, setSelectedListingId] = useState<string>('');
  const [csvFile, setCsvFile] = useState<File | null>(null);

  // Hooks
  const { listings, loading, refetch } = useListings();
  const createMutation = useCreateListingMutation({
    onSuccess: (response) => {
      toast({ title: 'Success', description: `Created listing with ID: ${response.data.id}` });
      refetch();
    },
    onError: (error) => {
      toast({ title: 'Error', description: `Failed to create: ${error.message}`, variant: 'destructive' });
    }
  });

  const updateMutation = useUpdateListingMutation({
    onSuccess: () => {
      toast({ title: 'Success', description: 'Listing updated successfully' });
      refetch();
    }
  });

  const deleteMutation = useDeleteListingMutation({
    onSuccess: () => {
      toast({ title: 'Success', description: 'Listing deleted successfully' });
      refetch();
    }
  });

  const saveDraftMutation = useSaveDraftListingMutation({
    onSuccess: (response) => {
      toast({ title: 'Success', description: `Draft saved with ID: ${response.data.id}` });
      refetch();
    }
  });

  const updateDraftMutation = useUpdateDraftListingMutation({
    onSuccess: () => {
      toast({ title: 'Success', description: 'Draft updated successfully' });
      refetch();
    }
  });

  const csvImportMutation = useImportListingsCsvMutation({
    onSuccess: (response) => {
      toast({ 
        title: 'Success', 
        description: `Imported ${response.data.created.length} listings, ${response.data.failed.length} failed` 
      });
      refetch();
    }
  });

  // Handlers
  const handleCreateListing = () => {
    const listingData: CreateListingRequest = {
      ...testData,
      details: {
        businessDescription: 'Test business description',
        briefDescription: 'Test brief description',
        financingAvailable: false,
      }
    };
    createMutation.mutate(listingData);
  };

  const handleSaveDraft = () => {
    const draftData: SaveDraftListingRequest = {
      ...testData,
      status: 'draft',
      details: {
        businessDescription: 'Draft business description',
        financingAvailable: false,
      }
    };
    saveDraftMutation.mutate(draftData);
  };

  const handleUpdateListing = () => {
    if (!selectedListingId) {
      toast({ title: 'Error', description: 'Please select a listing to update', variant: 'destructive' });
      return;
    }
    updateMutation.mutate({
      listingId: selectedListingId,
      listingData: {
        ...testData,
        businessName: testData.businessName + ' (Updated)',
        status: 'active'
      }
    });
  };

  const handleUpdateDraft = () => {
    if (!selectedListingId) {
      toast({ title: 'Error', description: 'Please select a draft to update', variant: 'destructive' });
      return;
    }
    updateDraftMutation.mutate({
      listingId: selectedListingId,
      listingData: {
        ...testData,
        businessName: testData.businessName + ' (Draft Updated)',
        status: 'draft'
      }
    });
  };

  const handleDeleteListing = () => {
    if (!selectedListingId) {
      toast({ title: 'Error', description: 'Please select a listing to delete', variant: 'destructive' });
      return;
    }
    deleteMutation.mutate(selectedListingId);
  };

  const handleCsvImport = () => {
    if (!csvFile) {
      toast({ title: 'Error', description: 'Please select a CSV file', variant: 'destructive' });
      return;
    }
    csvImportMutation.mutate(csvFile);
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <h1 className="text-3xl font-bold mb-8">Test Listing CRUD Operations</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Data Form */}
        <Card>
          <CardHeader>
            <CardTitle>Test Data</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="businessName">Business Name</Label>
              <Input
                id="businessName"
                value={testData.businessName}
                onChange={(e) => setTestData(prev => ({ ...prev, businessName: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="industry">Industry</Label>
              <Input
                id="industry"
                value={testData.industry}
                onChange={(e) => setTestData(prev => ({ ...prev, industry: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="askingPrice">Asking Price</Label>
              <Input
                id="askingPrice"
                type="number"
                value={testData.askingPrice}
                onChange={(e) => setTestData(prev => ({ ...prev, askingPrice: Number(e.target.value) }))}
              />
            </div>
            <div>
              <Label htmlFor="generalLocation">Location</Label>
              <Input
                id="generalLocation"
                value={testData.generalLocation}
                onChange={(e) => setTestData(prev => ({ ...prev, generalLocation: e.target.value }))}
              />
            </div>
          </CardContent>
        </Card>

        {/* CRUD Operations */}
        <Card>
          <CardHeader>
            <CardTitle>CRUD Operations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <Button 
                onClick={handleCreateListing} 
                disabled={createMutation.isPending}
                className="w-full"
              >
                {createMutation.isPending ? 'Creating...' : 'Create Listing'}
              </Button>
              <Button 
                onClick={handleSaveDraft} 
                disabled={saveDraftMutation.isPending}
                variant="outline"
                className="w-full"
              >
                {saveDraftMutation.isPending ? 'Saving...' : 'Save Draft'}
              </Button>
            </div>

            <div>
              <Label htmlFor="selectedListing">Select Listing for Update/Delete</Label>
              <select
                id="selectedListing"
                value={selectedListingId}
                onChange={(e) => setSelectedListingId(e.target.value)}
                className="w-full p-2 border rounded"
              >
                <option value="">Select a listing...</option>
                {listings.map((listing) => (
                  <option key={listing.id} value={listing.id}>
                    {listing.businessName} ({listing.status})
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <Button 
                onClick={handleUpdateListing} 
                disabled={updateMutation.isPending || !selectedListingId}
                variant="outline"
                className="w-full"
              >
                {updateMutation.isPending ? 'Updating...' : 'Update Listing'}
              </Button>
              <Button 
                onClick={handleUpdateDraft} 
                disabled={updateDraftMutation.isPending || !selectedListingId}
                variant="outline"
                className="w-full"
              >
                {updateDraftMutation.isPending ? 'Updating...' : 'Update Draft'}
              </Button>
            </div>

            <Button 
              onClick={handleDeleteListing} 
              disabled={deleteMutation.isPending || !selectedListingId}
              variant="destructive"
              className="w-full"
            >
              {deleteMutation.isPending ? 'Deleting...' : 'Delete Listing'}
            </Button>

            <div>
              <Label htmlFor="csvFile">CSV Import</Label>
              <Input
                id="csvFile"
                type="file"
                accept=".csv"
                onChange={(e) => setCsvFile(e.target.files?.[0] || null)}
              />
              <Button 
                onClick={handleCsvImport} 
                disabled={csvImportMutation.isPending || !csvFile}
                className="w-full mt-2"
              >
                {csvImportMutation.isPending ? 'Importing...' : 'Import CSV'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Listings */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Current Listings ({listings.length})</CardTitle>
          <Button onClick={() => refetch()} disabled={loading}>
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p>Loading listings...</p>
          ) : (
            <div className="space-y-2">
              {listings.map((listing) => (
                <div key={listing.id} className="p-3 border rounded">
                  <div className="font-medium">{listing.businessName}</div>
                  <div className="text-sm text-muted-foreground">
                    ID: {listing.id} | Status: {listing.status} | Industry: {listing.industry}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Price: ${listing.askingPrice?.toLocaleString()} | Location: {listing.generalLocation}
                  </div>
                </div>
              ))}
              {listings.length === 0 && (
                <p className="text-muted-foreground">No listings found</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
