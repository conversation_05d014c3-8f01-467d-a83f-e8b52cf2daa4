import { useNavigate } from "react-router-dom";
import UnifiedListingForm from "@/components/forms/UnifiedListingForm";

export default function AddListing() {
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate("/listings");
  };

  const handleCancel = () => {
    navigate("/listings");
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <UnifiedListingForm
        mode="create"
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}