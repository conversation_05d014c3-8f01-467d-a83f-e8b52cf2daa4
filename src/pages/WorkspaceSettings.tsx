import React from 'react';
import { WorkspaceSettings } from '@/components/workspace/WorkspaceSettings';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { UserRole } from '@/types';

const WorkspaceSettingsPage: React.FC = () => {
  return (
    <ProtectedRoute requiredRole={UserRole.ADMIN}>
      <div className="container mx-auto py-6 px-4">
        <WorkspaceSettings />
      </div>
    </ProtectedRoute>
  );
};

export default WorkspaceSettingsPage;