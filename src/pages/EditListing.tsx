
import { useParams, useNavigate } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import UnifiedListingForm from '@/components/forms/UnifiedListingForm';
import { useListing } from '@/hooks/useQueryApi';
import { LoadingOverlay } from '@/components/ui/loading-overlay';

export default function EditListing() {
  const { id } = useParams();
  const navigate = useNavigate();
  
  // Use React Query hook to fetch listing data
  const { listing, loading, error, refetch } = useListing(id);

  // Loading state
  if (loading) {
    return <LoadingOverlay
      message="Please wait while we fetch the listing details."
      source={`EditListing:/listings/${id}/edit:DataFetch`}
    />;
  }

  // Error state
  if (error || !listing) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-6xl">
        <div className="flex h-[60vh] items-center justify-center">
          <div className="text-center max-w-md">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">
              {error ? "Error Loading Listing" : "Listing Not Found"}
            </h2>
            <p className="text-muted-foreground mb-4">
              {error 
                ? "There was a problem loading the listing details. Please try again."
                : "The listing you're trying to edit doesn't exist."
              }
            </p>
            <div className="flex gap-2 justify-center">
              {error && (
                <Button onClick={() => refetch()} variant="outline">
                  Try Again
                </Button>
              )}
              <Button onClick={() => navigate('/listings')} variant="default">
                Back to Listings
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Convert API data to form format
  const convertToFormData = (listing: any) => {
    // Helper function to safely format currency
    const formatCurrencyForForm = (value: any) => {
      if (!value) return '';
      if (typeof value === 'string') {
        if (value.startsWith('$')) return value;
        const num = parseFloat(value.replace(/[,$]/g, ''));
        return isNaN(num) ? '' : `${num.toLocaleString()}`;
      }
      if (typeof value === 'number') {
        return `${value.toLocaleString()}`;
      }
      return '';
    };

    // Extract details object for easier access
    const details = listing.details || {};

    return {
      // Basic listing information
      businessName: listing.businessName || '',
      industry: listing.industry || '',
      askingPrice: formatCurrencyForForm(listing.askingPrice),
      cashFlow: formatCurrencyForForm(listing.cashFlowSde),
      status: listing.status || 'active',
      annualRevenue: formatCurrencyForForm(listing.annualRevenue),
      location: listing.generalLocation || '',
      yearEstablished: listing.yearEstablished?.toString() || '',
      employees: listing.employees?.toString() || '',
      ownerHours: listing.ownerHoursWeek?.toString() || '',
      
      // Business descriptions from details
      businessDescription: details.businessDescription || '',
      briefDescription: details.briefDescription || '',

      // Additional details from the details object
      growthOpportunities: Array.isArray(details.growthOpportunities)
        ? details.growthOpportunities.join('\n')
        : details.growthOpportunities || '',
      reasonForSale: details.reasonForSale || '',
      trainingPeriod: details.trainingPeriod || '',
      supportType: details.supportType || '',
      financingAvailable: details.financingAvailable || false,
      equipmentHighlights: Array.isArray(details.equipmentHighlights)
        ? details.equipmentHighlights.join('\n')
        : details.equipmentHighlights || '',
      supplierRelationships: details.supplierRelationships || '',
      realEstateStatus: details.realEstateStatus || ''
    };
  };

  const initialData = convertToFormData(listing);

  const handleSuccess = () => {
    navigate('/listings');
  };

  const handleCancel = () => {
    navigate('/listings');
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <UnifiedListingForm
        mode="edit"
        initialData={initialData}
        listingId={id}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}