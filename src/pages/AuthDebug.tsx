import React from 'react';
import { useAuth, usePermissions } from '@/contexts/AuthContext';
import { useOrganizationMembersQuery } from '@/hooks/useOrganizationApi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const AuthDebugPage: React.FC = () => {
  const { user, profile, loading, error } = useAuth();
  const { userRole, permissions, canViewWorkspaceSettings } = usePermissions();
  const { data: organizationMembers, isLoading: membersLoading, error: membersError } = useOrganizationMembersQuery();

  const currentUserMember = organizationMembers?.find(
    (member: any) => member.user.id === user?.id
  );

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      <h1 className="text-2xl font-bold">Authentication Debug</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* User Info */}
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>User ID:</strong> {user?.id || 'Not authenticated'}</div>
            <div><strong>Email:</strong> {user?.email || 'N/A'}</div>
            <div><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</div>
            {error && (
              <div className="text-red-600">
                <strong>Error:</strong> {error.message}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Profile Info */}
        <Card>
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Profile ID:</strong> {profile?.id || 'No profile'}</div>
            <div><strong>First Name:</strong> {profile?.first_name || 'N/A'}</div>
            <div><strong>Last Name:</strong> {profile?.last_name || 'N/A'}</div>
            <div><strong>Profile Role:</strong> {profile?.role || 'No role'}</div>
          </CardContent>
        </Card>

        {/* Organization Members */}
        <Card>
          <CardHeader>
            <CardTitle>Organization Members</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Members Loading:</strong> {membersLoading ? 'Yes' : 'No'}</div>
            <div><strong>Total Members:</strong> {organizationMembers?.length || 0}</div>
            {membersError && (
              <div className="text-red-600">
                <strong>Members Error:</strong> {membersError.message}
              </div>
            )}
            {currentUserMember && (
              <div className="mt-4 p-3 bg-gray-50 rounded">
                <div><strong>Current User Member:</strong></div>
                <div>ID: {currentUserMember.id}</div>
                <div>Role: <Badge>{currentUserMember.role}</Badge></div>
                <div>User ID: {currentUserMember.user.id}</div>
                <div>User Email: {currentUserMember.user.email}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>User Role (from permissions):</strong> <Badge>{userRole || 'No role'}</Badge></div>
            <div><strong>User Role Type:</strong> {typeof userRole}</div>
            <div><strong>User Role JSON:</strong> {JSON.stringify(userRole)}</div>
            <div><strong>Organization Member Role:</strong> <Badge>{currentUserMember?.role || 'No member role'}</Badge></div>
            <div><strong>Can View Workspace Settings:</strong> {canViewWorkspaceSettings() ? 'Yes' : 'No'}</div>
            <div><strong>Has ADMIN Role:</strong> {userRole === 'admin' ? 'Yes' : 'No'}</div>
            <div><strong>Has OWNER Role:</strong> {userRole === 'owner' ? 'Yes' : 'No'}</div>
            <div><strong>UserRole.ADMIN:</strong> {'admin'}</div>
            <div><strong>UserRole.OWNER:</strong> {'owner'}</div>
            <div><strong>Total Permissions:</strong> {permissions.length}</div>
            <div className="mt-4">
              <strong>All Permissions:</strong>
              <div className="flex flex-wrap gap-1 mt-2">
                {permissions.map(permission => (
                  <Badge key={permission} variant="outline" className="text-xs">
                    {permission}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Raw Data */}
      <Card>
        <CardHeader>
          <CardTitle>Raw Data (JSON)</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify({
              user,
              profile,
              userRole,
              organizationMembers,
              currentUserMember,
              permissions
            }, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthDebugPage;