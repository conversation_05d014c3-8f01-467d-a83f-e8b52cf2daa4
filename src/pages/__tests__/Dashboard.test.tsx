import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Dashboard from '../Dashboard';

// Mock the useListings hook
vi.mock('@/hooks/useQueryApi', () => ({
  useListings: vi.fn(),
}));

import { useListings } from '@/hooks/useQueryApi';

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Dashboard Loading States', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should show loading overlay for listings section while keeping other components visible', () => {
    // Mock loading state
    (useListings as any).mockReturnValue({
      listings: [],
      loading: true,
      error: null,
      pagination: { page: 1, limit: 12, total: 0, pages: 0 },
      isRefetching: false,
      isSuccess: false,
      isError: false,
      refetch: vi.fn(),
    });

    renderWithProviders(<Dashboard />);

    // Check that the main dashboard elements are visible
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Welcome back! Here\'s your business overview')).toBeInTheDocument();

    // Check that KPI cards are visible (they should render with fallback values)
    expect(screen.getByText('Active Listings')).toBeInTheDocument();

    // Check that loading overlay is shown for listings section
    expect(screen.getByText('Loading listings...')).toBeInTheDocument();
  });

  it('should show listings when loading is complete', () => {
    const mockListings = [
      {
        id: '1',
        businessName: 'Test Business 1',
        industry: 'Technology',
        askingPrice: 100000,
        status: 'Active',
      },
      {
        id: '2',
        businessName: 'Test Business 2',
        industry: 'Retail',
        askingPrice: 200000,
        status: 'Under Contract',
      },
    ];

    (useListings as any).mockReturnValue({
      listings: mockListings,
      loading: false,
      error: null,
      pagination: { page: 1, limit: 12, total: 2, pages: 1 },
      isRefetching: false,
      isSuccess: true,
      isError: false,
      refetch: vi.fn(),
    });

    renderWithProviders(<Dashboard />);

    // Check that listings are displayed
    expect(screen.getByText('Test Business 1')).toBeInTheDocument();
    expect(screen.getByText('Test Business 2')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
    expect(screen.getByText('Retail')).toBeInTheDocument();

    // Check that loading overlay is not shown
    expect(screen.queryByText('Loading listings...')).not.toBeInTheDocument();
  });

  it('should show error message when listings fail to load', () => {
    (useListings as any).mockReturnValue({
      listings: [],
      loading: false,
      error: new Error('Failed to fetch listings'),
      pagination: { page: 1, limit: 12, total: 0, pages: 0 },
      isRefetching: false,
      isSuccess: false,
      isError: true,
      refetch: vi.fn(),
    });

    renderWithProviders(<Dashboard />);

    // Check that error message is displayed
    expect(screen.getByText('Failed to load listings. Please try refreshing the page.')).toBeInTheDocument();

    // Check that loading overlay is not shown
    expect(screen.queryByText('Loading listings...')).not.toBeInTheDocument();
  });

  it('should show empty state when no listings are available', () => {
    (useListings as any).mockReturnValue({
      listings: [],
      loading: false,
      error: null,
      pagination: { page: 1, limit: 12, total: 0, pages: 0 },
      isRefetching: false,
      isSuccess: true,
      isError: false,
      refetch: vi.fn(),
    });

    renderWithProviders(<Dashboard />);

    // Check that empty state is displayed
    expect(screen.getByText('No listings yet')).toBeInTheDocument();
    expect(screen.getByText('Create your first listing to see it here.')).toBeInTheDocument();

    // Check that loading overlay is not shown
    expect(screen.queryByText('Loading listings...')).not.toBeInTheDocument();
  });

  it('should render KPI cards with fallback values during loading', () => {
    (useListings as any).mockReturnValue({
      listings: [],
      loading: true,
      error: null,
      pagination: { page: 1, limit: 12, total: 0, pages: 0 },
      isRefetching: false,
      isSuccess: false,
      isError: false,
      refetch: vi.fn(),
    });

    renderWithProviders(<Dashboard />);

    // Check that KPI cards are rendered with fallback values
    const activeListingsCards = screen.getAllByText('0');
    expect(activeListingsCards.length).toBeGreaterThan(0);
    
    // The dashboard should still be functional even during loading
    expect(screen.getByText('Active Listings')).toBeInTheDocument();
  });
});
