# Project Structure

## Root Directory

```
├── src/                 # Source code
├── public/             # Static assets
├── docs/               # Documentation
├── .kiro/              # Kiro configuration and steering
├── .env.example        # Environment template
└── package.json        # Dependencies and scripts
```

## Source Code Organization (`src/`)

### Core Application
- `main.tsx` - Application entry point with React Query setup
- `App.tsx` - Main app component with routing and providers
- `index.css` - Global styles and Tailwind imports
- `vite-env.d.ts` - Vite type definitions

### Component Architecture (`src/components/`)
- `ui/` - Reusable UI components (shadcn/ui)
- `auth/` - Authentication-related components
- `dashboard/` - Dashboard-specific components
- `forms/` - Form components (listings, CSV import)
- `layout/` - Layout components and app shell
- `reports/` - Reporting components
- `team/` - Team management components
- `workspace/` - Workspace settings components

### Application Logic
- `contexts/` - React contexts (Auth, Permissions, Workspace)
- `hooks/` - Custom React hooks for API and utilities
- `lib/` - Utility functions and configurations
- `pages/` - Route components/pages
- `types/` - TypeScript type definitions
- `models/` - Data validation schemas

### Data Layer
- `data/` - Mock data and constants
- `lib/api-client.ts` - API client configuration
- `lib/supabase.ts` - Supabase client setup
- `lib/query-client.ts` - React Query configuration

## Naming Conventions

### Files and Directories
- **Components**: PascalCase (e.g., `AccountSettings.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useApi.tsx`)
- **Utilities**: camelCase (e.g., `formatters.ts`)
- **Pages**: PascalCase (e.g., `Dashboard.tsx`)
- **Types**: camelCase (e.g., `api.ts`)

### Component Structure
- Use named exports for components
- Props interface named `ComponentNameProps`
- Default props and TypeScript strict typing
- Functional components with hooks

## Import Patterns

### Path Aliases
- `@/components` - UI components
- `@/hooks` - Custom hooks  
- `@/lib` - Utilities and configs
- `@/contexts` - React contexts
- `@/types` - Type definitions
- `@/pages` - Page components

### Import Order
1. React and external libraries
2. Internal components (using `@/` aliases)
3. Relative imports
4. Type-only imports (with `type` keyword)

## Architecture Patterns

### State Management
- **Server State**: React Query for API data
- **Client State**: React Context for auth/workspace
- **Form State**: React Hook Form with Zod validation
- **UI State**: Local component state with useState

### Component Patterns
- **Compound Components**: For complex UI (forms, modals)
- **Render Props**: For data fetching hooks
- **Higher-Order Components**: For route protection
- **Context Providers**: For cross-cutting concerns

### File Organization
- Group by feature/domain rather than file type
- Keep related components close together
- Separate business logic from UI components
- Use index files for clean imports