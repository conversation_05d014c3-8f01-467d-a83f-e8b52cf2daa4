# Product Overview

This is a **broker portfolio platform** frontend application built for real estate professionals. The platform enables brokers to manage their property listings, team members, and business operations through a comprehensive web interface.

## Key Features

- **Listing Management**: Create, edit, and manage property listings with detailed information
- **Team Management**: Collaborate with team members and manage workspace settings
- **Dashboard & Analytics**: View KPIs, recent listings, and generate reports
- **Authentication & Authorization**: Role-based access control with workspace-level permissions
- **CSV Import**: Bulk import listings from CSV files
- **Account Management**: User profiles, preferences, and security settings

## Target Users

- Real estate brokers and agents
- Property management teams
- Real estate agencies and brokerages

## Architecture

The application is designed as part of a larger monorepo but maintains its own git repository. It connects to:
- Supabase backend for authentication and data storage
- Custom API backend for business logic
- Cloudflare Pages for deployment