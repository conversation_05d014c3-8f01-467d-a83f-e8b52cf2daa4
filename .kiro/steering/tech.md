# Technology Stack

## Core Technologies

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite 5 with SWC for fast compilation
- **Routing**: React Router DOM v6
- **State Management**: React Query (TanStack Query) for server state
- **Styling**: Tailwind CSS with CSS variables for theming
- **UI Components**: shadcn/ui built on Radix UI primitives
- **Forms**: React Hook Form with Zod validation
- **Authentication**: Supabase Auth
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Cloudflare Pages via Wrangler

## Key Libraries

- **Icons**: Lucide React
- **Date Handling**: date-fns
- **Charts**: Recharts
- **Notifications**: Sonner
- **Carousel**: Embla Carousel
- **Themes**: next-themes for dark/light mode
- **Utilities**: clsx + tailwind-merge via `cn()` helper

## Development Tools

- **Package Manager**: Bun (with npm fallback)
- **Linting**: ESLint 9 with TypeScript support
- **Testing**: Vitest with Testing Library
- **Type Checking**: TypeScript 5.5+

## Common Commands

```bash
# Development
bun run dev              # Start dev server on port 8080
bun run build            # Production build
bun run preview          # Preview production build
bun run test             # Run tests
bun run test:run         # Run tests once

# Deployment
bun run build-deploy:prod     # Build and deploy to production
bun run build-deploy:preview  # Build and deploy to preview

# Maintenance
bun run lint             # Lint code
bun run update-types     # Update API types from backend
```

## Environment Setup

- Copy `.env.example` to `.env`
- Configure Supabase URL and anon key
- Set API URL for backend connection
- Development server runs on `localhost:8080`

## Build Configuration

- **Vite Config**: Optimized for production with compression (Brotli + Gzip)
- **TypeScript**: Relaxed settings for rapid development
- **Path Aliases**: `@/*` maps to `src/*`
- **Development**: Component tagging enabled via Lovable
- **Production**: Console removal and asset compression