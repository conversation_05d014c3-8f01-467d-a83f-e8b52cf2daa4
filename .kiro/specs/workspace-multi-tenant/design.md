# Design Document

## Overview

This design implements a workspace-centric multi-tenant architecture for the business brokerage application. Each workspace represents a broker company with complete data isolation, role-based access control, and collaborative features. The system transforms the current single-tenant application into a multi-tenant SaaS platform while maintaining the existing React/TypeScript architecture.

The design leverages Supabase for authentication, database, and real-time features, integrating seamlessly with the existing Vite + React + shadcn/ui stack. The architecture ensures scalability, security, and maintainability while providing a smooth user experience for both workspace owners and team members.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React App] --> B[Auth Context]
        A --> C[Workspace Context]
        A --> D[Permissions Context]
        B --> E[Supabase Auth]
    end
    
    subgraph "Backend Layer"
        E --> F[Supabase Database]
        F --> G[Row Level Security]
        F --> H[Database Functions]
    end
    
    subgraph "Data Layer"
        F --> I[Workspaces Table]
        F --> J[User Profiles Table]
        F --> K[Workspace Invitations]
        F --> L[Listings Table]
    end
    
    subgraph "External Services"
        M[Email Service]
        N[File Storage]
    end
    
    E --> M
    A --> N
```

### Multi-Tenant Data Architecture

The system implements tenant isolation at the database level using Row Level Security (RLS) policies. Each workspace acts as a tenant boundary, ensuring complete data separation between different broker companies.

**Tenant Isolation Strategy:**
- Workspace-scoped data access through RLS policies
- JWT claims include workspace context
- All database queries automatically filtered by workspace_id
- No cross-workspace data leakage possible

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant S as Supabase Auth
    participant D as Database
    
    U->>F: Sign In/Sign Up
    F->>S: Authenticate
    S->>D: Fetch User Profile
    D->>S: Return Profile + Workspace
    S->>F: JWT with Workspace Claims
    F->>F: Set Auth Context
    F->>U: Redirect to Workspace Dashboard
```

## Components and Interfaces

### Core Context Providers

#### AuthContext
Manages authentication state and user session information.

```typescript
interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  workspace: Workspace | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: SignUpData) => Promise<void>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
}
```

#### WorkspaceContext
Manages workspace-specific data and team information.

```typescript
interface WorkspaceContextType {
  workspace: Workspace | null;
  teamMembers: UserProfile[];
  invitations: WorkspaceInvitation[];
  updateWorkspace: (data: Partial<Workspace>) => Promise<void>;
  inviteTeamMember: (email: string, role: UserRole) => Promise<void>;
  removeTeamMember: (userId: string) => Promise<void>;
  updateMemberRole: (userId: string, role: UserRole) => Promise<void>;
}
```

#### PermissionsContext
Handles role-based access control and feature permissions.

```typescript
interface PermissionsContextType {
  hasPermission: (permission: Permission) => boolean;
  canAccessFeature: (feature: Feature) => boolean;
  canManageTeam: () => boolean;
  canManageBilling: () => boolean;
  canEditListing: (listingId: string) => boolean;
}
```

### Authentication Components

#### SignUpForm
Multi-step workspace owner registration form with company information collection.

**Features:**
- Company details collection
- Owner profile setup
- Email verification integration
- Form validation with Zod schemas
- Progress indication

#### SignInForm
Unified login form with workspace context resolution.

**Features:**
- Email/password authentication
- Workspace detection and routing
- "Remember me" functionality
- Password recovery integration

#### InvitationAcceptance
Component for handling team member invitations.

**Features:**
- Token validation
- User account creation or linking
- Profile completion
- Workspace onboarding

### Onboarding Components

#### OnboardingWizard
Five-step guided setup process for new workspace owners.

**Steps:**
1. **Company Profile**: Logo upload, branding, description
2. **Market Focus**: Target markets, price ranges, service areas
3. **Team Setup**: Initial team member invitations
4. **Sample Listing**: Guided first listing creation
5. **Preferences**: Notifications, dashboard layout

#### OnboardingProgress
Progress tracking component with step navigation and completion status.

### Workspace Management Components

#### WorkspaceSettings
Comprehensive workspace configuration interface.

**Sections:**
- Company information
- Branding and customization
- Team management
- Subscription and billing
- Security settings

#### TeamManagement
Interface for managing team members, roles, and invitations.

**Features:**
- Team member list with role indicators
- Invitation management (send, resend, revoke)
- Role assignment and updates
- Activity monitoring
- Bulk operations

### Protected Route System

#### ProtectedRoute
Route wrapper that enforces authentication and workspace access.

```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredPermission?: Permission;
  fallback?: React.ReactNode;
}
```

#### RoleGuard
Component-level access control for UI elements.

```typescript
interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  fallback?: React.ReactNode;
}
```

## Data Models

### Core Entities

#### Workspace
```typescript
interface Workspace {
  id: string;
  company_name: string;
  company_type: 'individual' | 'team' | 'firm';
  subscription_plan: 'trial' | 'basic' | 'pro' | 'enterprise';
  domain?: string;
  logo_url?: string;
  primary_color: string;
  address?: string;
  phone?: string;
  website?: string;
  license_number?: string;
  specialties: string[];
  target_markets: string[];
  status: 'active' | 'suspended' | 'trial' | 'cancelled';
  trial_ends_at?: string;
  onboarding_completed: boolean;
  onboarding_step: number;
  created_at: string;
  updated_at: string;
}
```

#### UserProfile
```typescript
interface UserProfile {
  id: string;
  workspace_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'owner' | 'admin' | 'broker' | 'viewer';
  phone?: string;
  license_number?: string;
  bio?: string;
  avatar_url?: string;
  specialties: string[];
  is_active: boolean;
  invited_at?: string;
  joined_at?: string;
  invited_by?: string;
  created_at: string;
  updated_at: string;
}
```

#### WorkspaceInvitation
```typescript
interface WorkspaceInvitation {
  id: string;
  workspace_id: string;
  email: string;
  role: 'admin' | 'broker' | 'viewer';
  invited_by: string;
  token: string;
  expires_at: string;
  accepted_at?: string;
  created_at: string;
}
```

#### Enhanced Listing
```typescript
interface Listing extends ExistingListing {
  workspace_id: string;
  created_by: string;
  assigned_to?: string;
  internal_notes?: InternalNote[];
  team_visibility: 'all' | 'assigned' | 'custom';
}

interface InternalNote {
  id: string;
  content: string;
  created_by: string;
  created_at: string;
  mentions: string[];
}
```

### Database Schema

#### Supabase Tables

**workspaces**
```sql
CREATE TABLE workspaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_name TEXT NOT NULL,
  company_type TEXT CHECK (company_type IN ('individual', 'team', 'firm')) DEFAULT 'team',
  subscription_plan TEXT CHECK (subscription_plan IN ('trial', 'basic', 'pro', 'enterprise')) DEFAULT 'trial',
  domain TEXT UNIQUE,
  logo_url TEXT,
  primary_color TEXT DEFAULT '#3B82F6',
  address TEXT,
  phone TEXT,
  website TEXT,
  license_number TEXT,
  specialties TEXT[],
  target_markets TEXT[],
  status TEXT CHECK (status IN ('active', 'suspended', 'trial', 'cancelled')) DEFAULT 'trial',
  trial_ends_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '14 days'),
  onboarding_completed BOOLEAN DEFAULT FALSE,
  onboarding_step INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**user_profiles**
```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  role TEXT CHECK (role IN ('owner', 'admin', 'broker', 'viewer')) NOT NULL,
  phone TEXT,
  license_number TEXT,
  bio TEXT,
  avatar_url TEXT,
  specialties TEXT[],
  is_active BOOLEAN DEFAULT TRUE,
  invited_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE,
  invited_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(workspace_id, email)
);
```

**workspace_invitations**
```sql
CREATE TABLE workspace_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT CHECK (role IN ('admin', 'broker', 'viewer')) NOT NULL,
  invited_by UUID REFERENCES auth.users(id),
  token TEXT UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(workspace_id, email)
);
```

#### Row Level Security Policies

```sql
-- Workspaces: Users can only access their workspace
CREATE POLICY "Users can only access their workspace"
ON workspaces FOR ALL
USING (id = (SELECT workspace_id FROM user_profiles WHERE id = auth.uid()));

-- User profiles: Users can only see workspace team members
CREATE POLICY "Users can only see workspace team members"
ON user_profiles FOR ALL
USING (workspace_id = (SELECT workspace_id FROM user_profiles WHERE id = auth.uid()));

-- Listings: Users can only see workspace listings
CREATE POLICY "Users can only see workspace listings"
ON listings FOR ALL
USING (workspace_id = (SELECT workspace_id FROM user_profiles WHERE id = auth.uid()));

-- Invitations: Only owners/admins can manage invitations
CREATE POLICY "Only workspace owners and admins can manage invitations"
ON workspace_invitations FOR ALL
USING (
  workspace_id = (SELECT workspace_id FROM user_profiles WHERE id = auth.uid())
  AND
  (SELECT role FROM user_profiles WHERE id = auth.uid()) IN ('owner', 'admin')
);
```

## Error Handling

### Authentication Errors
- Invalid credentials handling with user-friendly messages
- Session expiration with automatic refresh attempts
- Network connectivity issues with retry mechanisms
- Email verification failures with resend options

### Authorization Errors
- Insufficient permissions with clear explanations
- Workspace access denied with appropriate redirects
- Feature restrictions based on subscription plans
- Role-based access violations with helpful guidance

### Workspace Errors
- Workspace suspension handling with status pages
- Trial expiration with upgrade prompts
- Team member limit exceeded notifications
- Invitation token expiration with renewal options

### Data Validation Errors
- Form validation with real-time feedback
- API response validation with error boundaries
- File upload errors with retry mechanisms
- Database constraint violations with user-friendly messages

### Error Boundary Implementation
```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class WorkspaceErrorBoundary extends Component<Props, ErrorBoundaryState> {
  // Handles workspace-specific errors
  // Provides fallback UI for different error types
  // Logs errors for monitoring and debugging
}
```

## Testing Strategy

### Unit Testing
- Context providers with mock data
- Authentication flows with Supabase mocks
- Permission checking logic
- Form validation and submission
- Component rendering with different roles

### Integration Testing
- End-to-end authentication flows
- Workspace creation and onboarding
- Team invitation and acceptance
- Role-based access control
- Data isolation verification

### Security Testing
- Cross-workspace data access attempts
- Permission escalation attempts
- JWT token manipulation
- SQL injection prevention
- XSS vulnerability checks

### Performance Testing
- Large team workspace performance
- Concurrent user access patterns
- Database query optimization
- Real-time updates scalability
- File upload and storage performance

### Test Data Management
```typescript
// Test workspace factory
const createTestWorkspace = (overrides?: Partial<Workspace>): Workspace => {
  return {
    id: generateUUID(),
    company_name: 'Test Company',
    company_type: 'team',
    subscription_plan: 'trial',
    // ... default test values
    ...overrides
  };
};

// Test user factory with role variations
const createTestUser = (role: UserRole, workspaceId: string): UserProfile => {
  // Returns user with specified role and workspace
};
```

### Automated Testing Pipeline
- Pre-commit hooks for unit tests
- CI/CD integration with test suites
- Database migration testing
- Security vulnerability scanning
- Performance regression testing

This design provides a comprehensive foundation for implementing the multi-tenant workspace architecture while maintaining the existing application's structure and user experience. The modular approach allows for incremental implementation and testing, ensuring a smooth transition from single-tenant to multi-tenant architecture.