# API Specification - Multi-Tenant Business Brokerage Platform

## Overview

This document outlines the comprehensive API requirements for the multi-tenant business brokerage platform. The APIs are organized by functional domain and follow RESTful conventions with consistent response patterns.

## Base URL Structure

```
Production: https://api.yourdomain.com/v1
Development: http://localhost:3000/api/v1
```

## Authentication

All APIs (except public auth endpoints) require authentication via Bear<PERSON> token:

```
Authorization: Bearer <access_token>
```

## Standard Response Patterns

### Success Response

```typescript
{
  success: true,
  data: T,
  message?: string
}
```

### Error Response

```typescript
{
  success: false,
  error: {
    code: string,
    message: string,
    details?: any,
    field?: string // for validation errors
  }
}
```

### Paginated Response

```typescript
{
  success: true,
  data: T[],
  pagination: {
    page: number,
    limit: number,
    total: number,
    total_pages: number
  }
}
```

---

## 1. Authentication & User Management APIs

### Auth APIs

#### POST /auth/signup

**Description:** User registration with workspace creation
**Input:**

```typescript
{
  email: string;
  password: string;
  confirmPassword: string;
  first_name: string;
  last_name: string;
  company_name: string;
  company_type: 'individual' | 'team' | 'firm';
  phone?: string;
  license_number?: string;
  website?: string;
  address?: string;
  terms_accepted: boolean;
  marketing_consent?: boolean;
}
```

**Output:**

```typescript
{
  user: User;
  workspace: Workspace;
  profile: UserProfile;
  session: AuthSession;
}
```

#### POST /auth/signin

**Description:** User authentication with optional workspace selection
**Input:**

```typescript
{
  email: string;
  password: string;
  workspace_id?: string;
  remember_me?: boolean;
}
```

**Output:**

```typescript
{
  user: User;
  session: AuthSession;
  workspace: Workspace;
  profile: UserProfile;
}
```

#### POST /auth/signout

**Description:** User logout
**Input:**

```typescript
{
  refresh_token: string;
}
```

**Output:**

```typescript
{
  success: boolean;
}
```

#### POST /auth/refresh

**Description:** Refresh authentication token
**Input:**

```typescript
{
  refresh_token: string;
}
```

**Output:**

```typescript
{
  access_token: string;
  refresh_token: string;
  expires_at: number;
}
```

#### POST /auth/forgot-password

**Description:** Password reset request
**Input:**

```typescript
{
  email: string;
}
```

**Output:**

```typescript
{
  success: boolean;
  message: string;
}
```

#### POST /auth/reset-password

**Description:** Password reset confirmation
**Input:**

```typescript
{
  token: string;
  new_password: string;
  confirm_password: string;
}
```

**Output:**

```typescript
{
  success: boolean;
}
```

#### GET /auth/callback

**Description:** Handle email verification callback
**Query Parameters:** Token and verification parameters from email link
**Output:** Redirect to dashboard or error page

### User Profile APIs

> **Note on Avatar Uploads:** User avatars should be uploaded using the centralized `POST /files/upload` endpoint (see File Management APIs section) with `type=avatar` and `resource_type=user`. The returned `file_id` is then used in the `avatar_file_id` field when updating the user profile via `PUT /users/profile`.

#### GET /users/profile

**Description:** Get current user profile with workspace context
**Output:**

```typescript
{
  id: string;
  workspace_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  phone?: string;
  license_number?: string;
  bio?: string;
  avatar_url?: string;
  specialties: string[];
  is_active: boolean;
  invited_at?: string;
  joined_at?: string;
  invited_by?: string;
  preferences: {
    notifications: {
      email_notifications: boolean;
      push_notifications: boolean;
      listing_updates: boolean;
      team_updates: boolean;
      system_updates: boolean;
    };
    display: {
      timezone: string;
      date_format: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
      currency: string;
      language: string;
    };
    privacy: {
      profile_visibility: 'public' | 'team' | 'private';
      contact_visibility: 'public' | 'team' | 'private';
    };
  };
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}
```

#### PUT /users/profile

**Description:** Update user profile information (centralized endpoint for all profile updates)
**Input:**

```typescript
{
  // Basic Information
  first_name?: string;
  last_name?: string;
  phone?: string;
  license_number?: string;
  bio?: string;
  specialties?: string[];
  avatar_file_id?: string; // File ID from POST /files upload

  // Security Updates
  current_password?: string; // Required for password/email changes
  new_password?: string;
  confirm_password?: string;
  new_email?: string;

  // Preferences
  preferences?: {
    notifications?: {
      email_notifications?: boolean;
      push_notifications?: boolean;
      listing_updates?: boolean;
      team_updates?: boolean;
      system_updates?: boolean;
    };
    display?: {
      timezone?: string;
      date_format?: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
      currency?: string;
      language?: string;
    };
    privacy?: {
      profile_visibility?: 'public' | 'team' | 'private';
      contact_visibility?: 'public' | 'team' | 'private';
    };
  }
}
```

**Output:**

```typescript
{
  id: string;
  workspace_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  phone?: string;
  license_number?: string;
  bio?: string;
  avatar_url?: string;
  specialties: string[];
  is_active: boolean;
  preferences: UserPreferences;
  updated_at: string;

  // Response metadata for operations
  operations_completed: {
    profile_updated: boolean;
    password_changed?: boolean;
    email_change_initiated?: boolean;
    email_verification_sent?: boolean;
    avatar_updated?: boolean;
  };

  // Warnings or next steps
  requires_reauth?: boolean;
  warnings?: string[];
}
```

#### POST /users/verify-email

**Description:** Verify new email address (called after email change via PUT /users/profile)
**Input:**

```typescript
{
  token: string;
  email: string;
}
```

**Output:**

```typescript
{
  success: boolean;
  message: string;
  email_updated: boolean;
}
```

---

## 2. Workspace Management APIs

### Workspace APIs

#### GET /workspaces/current

**Description:** Get current workspace details
**Output:** `Workspace`

#### PUT /workspaces/current

**Description:** Update workspace settings
**Input:** `Partial<Workspace>`
**Output:** `Workspace`

#### GET /workspaces/subscription-status

**Description:** Get subscription and billing information
**Output:**

```typescript
{
  plan: SubscriptionPlan;
  status: WorkspaceStatus;
  trial_ends_at?: string;
  limits: SubscriptionLimits;
  current_usage: {
    team_members: number;
    listings: number;
    storage_gb: number;
  };
}
```

#### POST /workspaces/upgrade-subscription

**Description:** Upgrade subscription plan
**Input:**

```typescript
{
  plan: SubscriptionPlan;
  payment_method: {
    type: "card" | "bank";
    token: string;
  }
}
```

**Output:**

```typescript
{
  success: boolean;
  subscription: {
    plan: SubscriptionPlan;
    status: string;
    next_billing_date: string;
  }
}
```

### Team Management APIs

#### GET /workspaces/team-members

**Description:** Get all team members
**Query Parameters:**

- `include_inactive?: boolean`
- `role?: UserRole`
  **Output:** `UserProfile[]`

#### POST /workspaces/invite-member

**Description:** Invite team member
**Input:**

```typescript
{
  email: string;
  role: Exclude<UserRole, 'owner'>;
  first_name?: string;
  last_name?: string;
  message?: string;
  expires_in_days?: number;
}
```

**Output:** `WorkspaceInvitation`

#### PUT /workspaces/members/:userId/role

**Description:** Update member role
**Input:**

```typescript
{
  role: UserRole;
}
```

**Output:** `UserProfile`

#### DELETE /workspaces/members/:userId

**Description:** Remove team member (deactivate)
**Output:**

```typescript
{
  success: boolean;
}
```

#### GET /workspaces/invitations

**Description:** Get pending invitations
**Query Parameters:**

- `status?: 'pending' | 'expired' | 'accepted'`
  **Output:** `WorkspaceInvitation[]`

#### POST /workspaces/invitations/:id/resend

**Description:** Resend invitation
**Output:** `WorkspaceInvitation`

#### DELETE /workspaces/invitations/:id

**Description:** Revoke invitation
**Output:**

```typescript
{
  success: boolean;
}
```

#### POST /invitations/accept

**Description:** Accept workspace invitation
**Input:**

```typescript
{
  token: string;
  first_name: string;
  last_name: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  license_number?: string;
  bio?: string;
}
```

**Output:**

```typescript
{
  user: User;
  workspace: Workspace;
  profile: UserProfile;
}
```

---

## 3. Listing Management APIs

### Listings CRUD

#### GET /listings

**Description:** Get all listings for workspace
**Query Parameters:**

- `page?: number` (default: 1)
- `limit?: number` (default: 20, max: 100)
- `search?: string`
- `status?: 'Active' | 'Under Contract' | 'Closed' | 'Archived'`
- `industry?: string`
- `price_min?: number`
- `price_max?: number`
- `location?: string`
- `assigned_to?: string`
- `created_by?: string`
- `sort?: 'created_at' | 'updated_at' | 'asking_price' | 'listing_name'`
- `order?: 'asc' | 'desc'`
  **Output:**

```typescript
{
  listings: Listing[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}
```

#### GET /listings/:id

**Description:** Get specific listing
**Output:** `Listing`

#### POST /listings

**Description:** Create new listing
**Input:** `Omit<Listing, 'id' | 'created_at' | 'updated_at' | 'workspace_id'>`
**Output:** `Listing`

#### PUT /listings/:id

**Description:** Update listing
**Input:** `Partial<Listing>`
**Output:** `Listing`

#### DELETE /listings/:id

**Description:** Delete listing
**Output:**

```typescript
{
  success: boolean;
}
```

#### POST /listings/:id/duplicate

**Description:** Duplicate listing
**Input:**

```typescript
{
  new_name?: string;
  copy_images?: boolean;
  copy_documents?: boolean;
}
```

**Output:** `Listing`

### Listing Media & Documents

#### POST /listings/:id/images

**Description:** Upload listing images
**Input:** `FormData` with image files (max 10 files, 5MB each)
**Output:**

```typescript
{
  image_urls: string[];
  uploaded_count: number;
}
```

#### DELETE /listings/:id/images/:imageId

**Description:** Delete listing image
**Output:**

```typescript
{
  success: boolean;
}
```

#### POST /listings/:id/documents

**Description:** Upload listing documents
**Input:** `FormData` with document files (max 20 files, 10MB each)
**Output:**

```typescript
{
  document_urls: string[];
  uploaded_count: number;
}
```

#### DELETE /listings/:id/documents/:documentId

**Description:** Delete listing document
**Output:**

```typescript
{
  success: boolean;
}
```

### Listing Collaboration

#### GET /listings/:id/notes

**Description:** Get internal notes
**Query Parameters:**

- `limit?: number`
- `offset?: number`
  **Output:** `InternalNote[]`

#### POST /listings/:id/notes

**Description:** Add internal note
**Input:**

```typescript
{
  content: string;
  mentions?: string[]; // User IDs to mention
}
```

**Output:** `InternalNote`

#### PUT /listings/:id/notes/:noteId

**Description:** Update internal note
**Input:**

```typescript
{
  content: string;
}
```

**Output:** `InternalNote`

#### DELETE /listings/:id/notes/:noteId

**Description:** Delete internal note
**Output:**

```typescript
{
  success: boolean;
}
```

#### PUT /listings/:id/assign

**Description:** Assign listing to team member
**Input:**

```typescript
{
  assigned_to: string; // User ID
}
```

**Output:** `Listing`

---

## 4. Portfolio Management APIs

#### GET /portfolios

**Description:** Get all portfolios
**Query Parameters:**

- `created_by?: string`
- `template?: string`
  **Output:** `Portfolio[]`

#### GET /portfolios/:id

**Description:** Get specific portfolio with listings
**Output:**

```typescript
{
  portfolio: Portfolio;
  listings: Listing[];
}
```

#### POST /portfolios

**Description:** Create portfolio
**Input:**

```typescript
{
  name: string;
  description?: string;
  listing_ids: string[];
  template?: string;
}
```

**Output:** `Portfolio`

#### PUT /portfolios/:id

**Description:** Update portfolio
**Input:** `Partial<Portfolio>`
**Output:** `Portfolio`

#### DELETE /portfolios/:id

**Description:** Delete portfolio
**Output:**

```typescript
{
  success: boolean;
}
```

#### GET /portfolios/:id/export

**Description:** Export portfolio as PDF or HTML
**Query Parameters:**

- `format: 'pdf' | 'html'`
- `template?: string`
  **Output:**

```typescript
{
  download_url: string;
  expires_at: string;
}
```

---

## 5. Dashboard & Analytics APIs

### Dashboard APIs

#### GET /dashboard/kpis

**Description:** Get key performance indicators
**Query Parameters:**

- `period?: 'week' | 'month' | 'quarter' | 'year'`
  **Output:**

```typescript
{
  active_listings: number;
  under_contract: number;
  closed_this_month: number;
  pipeline_commissions: number;
  trends: {
    listings_change: number;
    commissions_change: number;
  }
}
```

#### GET /dashboard/recent-activity

**Description:** Get recent activity feed
**Query Parameters:**

- `limit?: number` (default: 10)
- `types?: string[]` // Activity types to include
  **Output:**

```typescript
{
  activities: {
    id: string;
    type: string;
    description: string;
    user: UserProfile;
    resource_type: string;
    resource_id: string;
    created_at: string;
  }
  [];
}
```

#### GET /dashboard/recent-listings

**Description:** Get recently created listings
**Query Parameters:**

- `limit?: number` (default: 5)
  **Output:** `Listing[]`

### Analytics APIs

#### GET /analytics/listings

**Description:** Listing analytics
**Query Parameters:**

- `period: 'week' | 'month' | 'quarter' | 'year'`
- `start_date?: string`
- `end_date?: string`
  **Output:**

```typescript
{
  total: number;
  by_status: Record<string, number>;
  by_industry: Record<string, number>;
  trends: {
    period: string;
    count: number;
  }
  [];
  average_price: number;
  median_price: number;
}
```

#### GET /analytics/revenue

**Description:** Revenue analytics
**Query Parameters:**

- `period: 'week' | 'month' | 'quarter' | 'year'`
  **Output:**

```typescript
{
  total_commissions: number;
  pipeline_value: number;
  closed_deals: number;
  average_commission: number;
  trends: {
    period: string;
    commissions: number;
    deals: number;
  }
  [];
}
```

#### GET /analytics/team-performance

**Description:** Team performance metrics
**Query Parameters:**

- `period: 'week' | 'month' | 'quarter' | 'year'`
  **Output:**

```typescript
{
  by_member: {
    user: UserProfile;
    listings_created: number;
    deals_closed: number;
    total_commissions: number;
    average_deal_size: number;
  }
  [];
}
```

---

## 6. AI & Automation APIs

#### POST /ai/generate-description

**Description:** Generate listing description using AI
**Input:**

```typescript
{
  business_type: string;
  industry: string;
  key_details: {
    revenue?: number;
    employees?: number;
    location?: string;
    key_assets?: string;
    reason_for_sale?: string;
  };
  tone?: 'professional' | 'casual' | 'compelling';
  length?: 'short' | 'medium' | 'long';
}
```

**Output:**

```typescript
{
  generated_text: string;
  suggestions: string[];
}
```

#### POST /ai/generate-business-model

**Description:** Generate business model description
**Input:**

```typescript
{
  industry: string;
  revenue: number;
  employees: number;
  key_features: string[];
  target_market?: string;
}
```

**Output:**

```typescript
{
  generated_text: string;
}
```

#### POST /ai/suggest-improvements

**Description:** Suggest listing improvements
**Input:**

```typescript
{
  listing_data: Partial<Listing>;
}
```

**Output:**

```typescript
{
  suggestions: {
    category: string;
    suggestion: string;
    priority: "high" | "medium" | "low";
  }
  [];
}
```

---

## 7. Search & Filtering APIs

#### GET /search/listings

**Description:** Advanced listing search with faceted results
**Query Parameters:**

- `q?: string` // General search query
- `industry?: string[]`
- `price_min?: number`
- `price_max?: number`
- `revenue_min?: number`
- `revenue_max?: number`
- `location?: string[]`
- `status?: string[]`
- `employee_count_min?: number`
- `employee_count_max?: number`
- `sort?: string`
- `page?: number`
- `limit?: number`
  **Output:**

```typescript
{
  results: Listing[];
  facets: {
    industries: { name: string; count: number }[];
    locations: { name: string; count: number }[];
    price_ranges: { range: string; count: number }[];
  };
  total: number;
  page: number;
  limit: number;
}
```

#### GET /search/suggestions

**Description:** Search suggestions/autocomplete
**Query Parameters:**

- `q: string`
- `type: 'industry' | 'location' | 'business_name'`
- `limit?: number`
  **Output:**

```typescript
{
  suggestions: string[];
}
```

---

## 8. Notification & Communication APIs

### Notification APIs

#### GET /notifications

**Description:** Get user notifications
**Query Parameters:**

- `unread_only?: boolean`
- `type?: string[]`
- `limit?: number`
- `offset?: number`
  **Output:**

```typescript
{
  notifications: {
    id: string;
    type: string;
    title: string;
    message: string;
    read: boolean;
    data?: any;
    created_at: string;
  }[];
  unread_count: number;
}
```

#### PUT /notifications/:id/read

**Description:** Mark notification as read
**Output:**

```typescript
{
  success: boolean;
}
```

#### PUT /notifications/mark-all-read

**Description:** Mark all notifications as read
**Output:**

```typescript
{
  success: boolean;
  marked_count: number;
}
```

### Email APIs

#### POST /emails/send-invitation

**Description:** Send workspace invitation email
**Input:**

```typescript
{
  invitation_id: string;
}
```

**Output:**

```typescript
{
  success: boolean;
  message_id: string;
}
```

#### POST /emails/send-listing-inquiry

**Description:** Send listing inquiry email
**Input:**

```typescript
{
  listing_id: string;
  contact_info: {
    name: string;
    email: string;
    phone?: string;
  };
  message: string;
  copy_sender?: boolean;
}
```

**Output:**

```typescript
{
  success: boolean;
  message_id: string;
}
```

---

## 9. File Management APIs

#### POST /files/upload

**Description:** Generic file upload with metadata
**Input:** `FormData` with file and metadata
**Query Parameters:**

- `type: 'image' | 'document' | 'avatar' | 'logo'`
- `resource_type?: 'listing' | 'workspace' | 'user'`
- `resource_id?: string`
  **Output:**

```typescript
{
  file_url: string;
  file_id: string;
  metadata: {
    filename: string;
    size: number;
    mime_type: string;
    dimensions?: { width: number; height: number };
  };
}
```

#### DELETE /files/:fileId

**Description:** Delete file
**Output:**

```typescript
{
  success: boolean;
}
```

#### GET /files/:fileId/signed-url

**Description:** Get signed URL for private files
**Query Parameters:**

- `expires_in?: number` // Seconds, default 3600
  **Output:**

```typescript
{
  signed_url: string;
  expires_at: string;
}
```

---

## 10. Integration APIs

#### GET /integrations/available

**Description:** Get available third-party integrations
**Output:**

```typescript
{
  integrations: {
    type: string;
    name: string;
    description: string;
    status: 'available' | 'connected' | 'error';
    features: string[];
  }[];
}
```

#### POST /integrations/:type/connect

**Description:** Connect external service
**Input:** Integration-specific credentials and configuration
**Output:**

```typescript
{
  success: boolean;
  connection_status: string;
  features_enabled: string[];
}
```

#### DELETE /integrations/:type/disconnect

**Description:** Disconnect external service
**Output:**

```typescript
{
  success: boolean;
}
```

---

## 11. Audit & Activity APIs

#### GET /activity

**Description:** Get workspace activity log
**Query Parameters:**

- `user_id?: string`
- `resource_type?: 'listing' | 'workspace' | 'user' | 'portfolio'`
- `resource_id?: string`
- `action?: string`
- `start_date?: string`
- `end_date?: string`
- `limit?: number`
- `offset?: number`
  **Output:**

```typescript
{
  activities: {
    id: string;
    user: UserProfile;
    action: string;
    resource_type: string;
    resource_id: string;
    resource_name?: string;
    metadata?: any;
    ip_address?: string;
    user_agent?: string;
    created_at: string;
  }[];
  total: number;
}
```

#### POST /activity/log

**Description:** Log custom activity (internal use)
**Input:**

```typescript
{
  action: string;
  resource_type: string;
  resource_id: string;
  metadata?: any;
}
```

**Output:**

```typescript
{
  success: boolean;
}
```

---

## Error Codes Reference

### Authentication Errors

- `AUTH_001`: Invalid credentials
- `AUTH_002`: Email not verified
- `AUTH_003`: Account locked
- `AUTH_004`: Session expired
- `AUTH_005`: Weak password
- `AUTH_006`: Email already exists
- `AUTH_007`: Signup disabled
- `AUTH_008`: Network error

### Workspace Errors

- `WORKSPACE_001`: Workspace not found
- `WORKSPACE_002`: Workspace suspended
- `WORKSPACE_003`: Trial expired
- `WORKSPACE_004`: Subscription required
- `WORKSPACE_005`: Team limit exceeded
- `WORKSPACE_006`: Listing limit exceeded
- `WORKSPACE_007`: Storage limit exceeded
- `WORKSPACE_008`: Feature not available
- `WORKSPACE_009`: Access denied

### Permission Errors

- `PERM_001`: Insufficient permissions
- `PERM_002`: Role required
- `PERM_003`: Feature restricted
- `PERM_004`: Resource access denied
- `PERM_005`: Action not allowed

### Validation Errors

- `VAL_001`: Required field missing
- `VAL_002`: Invalid format
- `VAL_003`: Value out of range
- `VAL_004`: Duplicate value
- `VAL_005`: Invalid file type
- `VAL_006`: File too large

### Rate Limiting

- `RATE_001`: Too many requests
- `RATE_002`: API quota exceeded
- `RATE_003`: Concurrent request limit

## Rate Limiting

- **Authentication endpoints**: 5 requests per minute per IP
- **File upload endpoints**: 10 requests per minute per user
- **Search endpoints**: 60 requests per minute per user
- **General API endpoints**: 1000 requests per hour per user
- **AI generation endpoints**: 20 requests per hour per user

## Webhook Support

The platform supports webhooks for real-time notifications:

### Available Events

- `listing.created`
- `listing.updated`
- `listing.status_changed`
- `team_member.invited`
- `team_member.joined`
- `workspace.subscription_changed`
- `portfolio.created`
- `note.mentioned`

### Webhook Configuration

Configure webhooks via the workspace settings API or dashboard interface.

## API Versioning

- Current version: `v1`
- Version specified in URL path: `/api/v1/`
- Backward compatibility maintained for at least 12 months
- Deprecation notices provided 6 months in advance

## Security Considerations

- All endpoints use HTTPS in production
- API keys and tokens should be kept secure
- File uploads are scanned for malware
- Rate limiting prevents abuse
- Input validation prevents injection attacks
- Audit logging tracks all API usage
