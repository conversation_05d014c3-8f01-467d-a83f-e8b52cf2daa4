# Draft Listing Implementation

## Overview

This document describes the implementation of the new `/v1/listings/draft` API endpoint integration for saving listing drafts with incomplete data.

## Changes Made

### 1. API Client Updates

#### Generated API Client (`src/lib/generated-api-client.ts`)
- Added `saveDraftListing` method to the `TypedApiClient` class
- Method accepts `SaveDraftListingRequest` data and returns `SingleListingResponse`
- Endpoint: `POST /v1/listings/draft`

```typescript
async saveDraftListing(data: components["schemas"]["SaveDraftListingRequest"]) {
  return this.post<components["schemas"]["SingleListingResponse"]>(
    '/v1/listings/draft',
    data
  );
}
```

#### Main API Client (`src/lib/api-client.ts`)
- Added `SaveDraftListingRequest` type export
- Added `saveDraftListing` method that wraps the generated client method
- Handles authentication and response processing

```typescript
async saveDraftListing(accessToken: string, listing: SaveDraftListingRequest): Promise<ListingResponse> {
  this.client.setAuth(accessToken);
  const response = await this.handleRequest(this.client.saveDraftListing(listing));
  return response.data;
}
```

### 2. React Query Hook

#### New Hook (`src/hooks/useQueryApi.tsx`)
- Added `useSaveDraftListingMutation` hook
- Provides optimistic updates and cache invalidation
- Handles success/error states and toast notifications

```typescript
export function useSaveDraftListingMutation(options: MutationOptions = {}) {
  // Implementation handles:
  // - Authentication token validation
  // - API call to draft endpoint
  // - Cache updates and invalidation
  // - Success/error handling
}
```

### 3. Form Component Updates

#### ListingForm.tsx
- Updated imports to include `useSaveDraftListingMutation` and `SaveDraftListingRequest`
- Added `saveDraftListingMutation` hook initialization
- Updated `handleSaveDraft` function to use the dedicated draft endpoint

#### ListingForm2.tsx
- Same updates as ListingForm.tsx for consistency

### 4. Key Improvements

#### Before (Old Implementation)
- Used regular create/update endpoints with `status: 'draft'`
- Required all mandatory fields to be present
- Could fail validation for incomplete data
- Same logic for all draft operations

#### After (New Implementation)
- **New drafts**: Uses dedicated `POST /v1/listings/draft` endpoint for incomplete data
- **Editing existing drafts**: Uses `PUT /v1/listings/{listingId}` endpoint for updates
- Smart routing based on `isEditing` and `listingId` props
- Better error handling for incomplete data

## API Schema

The `SaveDraftListingRequest` schema allows all fields to be optional except:
- `status`: Required, defaults to "draft"
- `listingType`: Required, defaults to "business_sale"  
- `teamVisibility`: Required, defaults to "all"

This enables saving listings with minimal data and progressively building them up.

## Logic Flow

The draft saving logic now works as follows:

### Creating New Drafts
- **Condition**: `isEditing = false` OR `listingId` is not provided
- **Endpoint**: `POST /v1/listings/draft`
- **Purpose**: Handles incomplete data gracefully
- **Use Case**: User starts a new listing and wants to save progress

### Editing Existing Drafts
- **Condition**: `isEditing = true` AND `listingId` is provided
- **Endpoint**: `PUT /v1/listings/{listingId}`
- **Purpose**: Updates existing listing (draft or otherwise) with new data
- **Use Case**: User returns to edit a previously saved draft

This approach provides the best of both worlds:
- New drafts benefit from the lenient validation of the draft endpoint
- Existing drafts can be updated using the standard, well-tested update flow

## Usage Example

```typescript
// In a form component
const saveDraftMutation = useSaveDraftListingMutation();
const updateMutation = useUpdateListingMutation();

const handleSaveDraft = async () => {
  const draftData = {
    businessName: 'Partial Business Name',
    status: 'draft',
    listingType: 'business_sale',
    teamVisibility: 'all',
    // Other fields can be omitted for new drafts
  };

  if (isEditing && listingId) {
    // Editing existing draft - use regular update endpoint
    await updateMutation.mutateAsync({
      listingId,
      listingData: draftData
    });
  } else {
    // Creating new draft - use dedicated draft endpoint
    await saveDraftMutation.mutateAsync(draftData);
  }
};
```

## Benefits

1. **Better UX**: Users can save incomplete listings without validation errors
2. **Proper API Usage**: Uses the appropriate endpoint for each scenario
3. **Smart Routing**: Automatically chooses the right endpoint based on context
4. **Type Safety**: Full TypeScript support with proper types
5. **Consistent Behavior**: Same implementation across all form components
6. **Flexible Updates**: Existing drafts can be updated using standard update flow

## Testing

The implementation has been tested by:
- Building the application successfully (no TypeScript errors)
- Running the development server without issues
- Verifying all imports and exports are correct
- Ensuring the API client methods are properly integrated

## Future Considerations

- Consider adding draft auto-save functionality
- Implement draft listing management (list, delete drafts)
- Add draft validation warnings in the UI
- Consider draft expiration policies
