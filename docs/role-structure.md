# Role Structure Documentation

## Overview

This application uses a dual-role system with two separate plugin roles:

1. **admin() plugin role**: System-level administration
2. **org() plugin role**: Organization-level team management

## Role Definitions

### admin() Plugin Roles
- **admin**: System administrators (future implementation)
- **user**: Regular users (current default for all users)

**Current State**: All users are assigned `admin.user` until admin management features are implemented.

### org() Plugin Roles
- **owner**: Full workspace access, assigned on signup
- **admin**: Can manage team members and workspace settings
- **member**: Can access and manage listings and basic features
- **viewer**: Read-only access (future implementation)

## User Journey

### On Signup
Every new user receives:
- `admin.user` role (system level)
- `org.owner` role (organization level)

### Team Management
- **Owners** can invite and manage all team members
- **Admins** can invite and manage members and viewers (but not other admins or owners)
- **Members** have basic workspace access
- **Viewers** have read-only access (when implemented)

## API Limitations

The current API schema only supports inviting users with `admin` and `member` roles. The `owner` role is automatically assigned on signup and cannot be changed via the invitation API.

## Implementation Notes

### Frontend Components
- Team management components handle all role types (`owner`, `admin`, `member`, `viewer`)
- Role-based permissions are enforced in the UI
- Visual indicators (icons, colors) differentiate between roles

### API Integration
- Invitation API only accepts `admin` and `member` roles
- Role updates via API are limited to `admin` and `member`
- Owner role management requires separate implementation

## Future Enhancements

1. **Admin Management**: Implement system-level admin features
2. **Viewer Role**: Complete implementation of read-only access
3. **Role Transitions**: Allow owners to transfer ownership
4. **Granular Permissions**: More fine-grained permission system

## Security Considerations

- Owner role cannot be assigned via invitation (security measure)
- Role changes are validated on both frontend and backend
- Permission checks prevent unauthorized access to management features