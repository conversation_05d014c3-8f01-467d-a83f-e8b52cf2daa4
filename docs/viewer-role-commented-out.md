# UserRole.VIEWER Commented Out

## Summary

All references to `UserRole.VIEWER` have been commented out across the entire application to disable the viewer role functionality.

## Files Modified

### Core Type Definition
- **`src/types/index.ts`**: Commented out `VIEWER = 'viewer'` in the UserRole enum

### Role Mapping
- **`src/lib/role-mapping.ts`**: Commented out the VIEWER role mapping in ROLE_LABELS

### Authentication & Permissions
- **`src/contexts/AuthContext.tsx`**: Commented out the entire VIEWER permissions array
- **`src/hooks/usePermissions.tsx`**: 
  - Commented out VIEWER in roleHierarchy mapping
  - Commented out VIEWER in roleNames mapping

### Pages
- **`src/pages/Reports.tsx`**: Changed from `UserRole.VIEWER` to `UserRole.MEMBER` for the required role

### Team Management Components
- **`src/components/team/TeamOverview.tsx`**:
  - Commented out viewer case in getRoleIcon()
  - Commented out viewer case in getRoleColor()
  - Updated role comment to indicate viewer is disabled

- **`src/components/team/MemberRoleManager.tsx`**:
  - Commented out viewer case in getRoleIcon()
  - Commented out viewer case in getRoleColor()

- **`src/components/team/MembersList.tsx`**:
  - Commented out viewer case in getRoleIcon()
  - Commented out viewer case in getRoleColor()
  - Updated role comments to indicate viewer is disabled

## Impact

1. **Role Hierarchy**: The role hierarchy now only includes MEMBER (2), ADMIN (3), and OWNER (4)
2. **Permissions**: No viewer-specific permissions are granted
3. **UI Components**: Team management components no longer display viewer-specific icons or styling
4. **Reports Access**: Reports page now requires MEMBER role instead of VIEWER role
5. **Type Safety**: All TypeScript types still compile correctly with VIEWER commented out

## Future Re-enabling

To re-enable the VIEWER role in the future:
1. Uncomment all the lines marked with `// [UserRole.VIEWER]` or similar
2. Update the role hierarchy numbers if needed
3. Test all team management functionality
4. Verify permission system works correctly

## Build Status

✅ TypeScript compilation passes
✅ Vite build succeeds
✅ No runtime errors expected

The application now operates with a three-tier role system: OWNER > ADMIN > MEMBER.