# React Query Usage Guide

## API Hooks Reference

### Query Hooks (Data Fetching)

```typescript
// Fetch listings with parameters
const { listings, pagination, loading, error, refetch } = useListings({
  page: 1,
  limit: 20,
  search: 'restaurant',
  status: 'active',
  sort_by: 'created_at',
  sort_order: 'desc'
});

// Fetch single listing
const { listing, loading, error, refetch } = useListing(listingId);

// Fetch user profile
const { data: profile, loading, error } = useUserProfileQuery();
```

### Mutation Hooks (Data Modification)

```typescript
// Create listing
const createMutation = useCreateListingMutation({
  onSuccess: (data) => {
    navigate(`/listings/${data.data.id}`);
  }
});

// Update listing
const updateMutation = useUpdateListingMutation();

// Delete listing
const deleteMutation = useDeleteListingMutation();

// Update listing status
const statusMutation = useUpdateListingStatusMutation();

// File operations
const uploadMutation = useUploadFileMutation();
const deleteMutation = useDeleteFileMutation();
```

### Usage Examples

```typescript
// Basic listing fetch
function ListingsPage() {
  const { 
    listings, 
    pagination, 
    loading, 
    error, 
    isRefetching 
  } = useListings();

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div>
      {listings.map(listing => (
        <ListingCard key={listing.id} listing={listing} />
      ))}
    </div>
  );
}

// Create listing with optimistic updates
function CreateListingForm() {
  const createListing = useCreateListingMutation({
    onSuccess: (data) => {
      toast.success('Listing created!');
      navigate(`/listings/${data.data.id}`);
    }
  });

  const handleSubmit = (formData) => {
    createListing.mutate(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* form fields */}
      <button 
        type="submit" 
        disabled={createListing.isPending}
      >
        {createListing.isPending ? 'Creating...' : 'Create Listing'}
      </button>
    </form>
  );
}
```

## Cache Management

### Force Refetch
```typescript
const { refetch } = useListings();
// Call refetch() when needed
```

### Clear Cache
```typescript
const queryClient = useQueryClient();
queryClient.invalidateQueries({ queryKey: ['listings'] });
``` 