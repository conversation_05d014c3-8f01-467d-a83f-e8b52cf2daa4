# Two-Token Authentication Strategy Implementation

## Overview

This document describes the implementation of a two-token authentication strategy in the broker-portfolio-forge application. The strategy enhances security by separating access tokens (stored in memory) from refresh tokens (stored in secure cookies), while ensuring user data is only stored in memory and sessions can be restored after page refresh.

## Architecture

### Token Storage Strategy

1. **Access Token**: Stored in React state (memory only)
   - Short-lived (typically 15-60 minutes)
   - Lost on page refresh
   - Used for API authentication
   - Cannot be accessed by malicious scripts after page refresh

2. **Refresh Token**: Stored in httpOnly cookies
   - Long-lived (typically 7 days)
   - Secure, httpOnly, SameSite=Strict
   - Used to obtain new access tokens
   - Cannot be accessed by JavaScript

3. **User Data**: Stored in React state (memory only)
   - User profile information
   - Workspace data
   - Lost on page refresh, restored via API call

## Implementation Details

### AuthContext (`src/contexts/AuthContext.tsx`)

The main authentication context implements the two-token strategy:

```typescript
// State stored in memory only
const [user, setUser] = useState<User | null>(null);
const [profile, setProfile] = useState<UserProfile | null>(null);
const [workspace, setWorkspace] = useState<Workspace | null>(null);
const [accessToken, setAccessToken] = useState<string | null>(null);

// Refresh token management
const storeRefreshToken = useCallback((refreshToken: string) => {
  document.cookie = `refresh_token=${refreshToken}; path=/; secure; samesite=strict; max-age=${7 * 24 * 60 * 60}`;
}, []);
```

### Session Restoration Flow

1. **Page Load**: Check for refresh token in cookies
2. **Token Refresh**: Use refresh token to get new access token
3. **User Data Fetch**: Use new access token to fetch user data
4. **State Update**: Update React state with user data and access token

```typescript
const initializeAuth = async () => {
  const refreshToken = getRefreshToken();
  if (refreshToken) {
    try {
            // Try to refresh the session (this will also restore user data)
            await refreshSession();
            
            console.log('Session restored via refresh token');
    } catch (error) {
      // Handle refresh failure
      clearRefreshToken();
    }
  }
};
```

### API Integration

All API hooks have been updated to use the access token from AuthContext:

```typescript
// Before (using localStorage)
const token = requireAuth(); // Gets from localStorage

// After (using AuthContext)
const { accessToken } = useAuth();
if (!accessToken) {
  throw new Error('No access token available. Please log in.');
}
```

## Security Benefits

1. **XSS Protection**: Access tokens in memory are lost on page refresh
2. **CSRF Protection**: Refresh tokens in httpOnly cookies cannot be accessed by JavaScript
3. **Token Rotation**: Both tokens are rotated on refresh
4. **Minimal Exposure**: Access tokens have short lifespans
5. **No localStorage**: No sensitive data persists in browser storage

## Files Modified

### Core Authentication
- `src/contexts/AuthContext.tsx` - Main authentication context with two-token strategy
- `src/lib/supabase.ts` - Disabled session persistence

### API Integration
- `src/hooks/useApi.tsx` - Updated to use AuthContext instead of localStorage
- `src/hooks/useQueryApi.tsx` - Updated all queries and mutations to use AuthContext
- `src/lib/query-client.ts` - Deprecated localStorage functions

### Testing
- `src/components/auth/SessionTest.tsx` - Test component to verify implementation

## Usage Examples

### Sign In
```typescript
const { signIn } = useAuth();
await signIn(email, password, workspaceId);
// Access token stored in memory, refresh token in cookie
```

### API Calls
```typescript
const { accessToken } = useAuth();
const response = await apiClient.getListings(accessToken, params);
```

### Session Check
```typescript
const { user, accessToken, loading } = useAuth();
if (loading) return <Loading />;
if (!user || !accessToken) return <SignIn />;
return <Dashboard />;
```

## Testing the Implementation

Use the `SessionTest` component to verify:

1. **Token Storage**: Verify access token in memory, refresh token in cookie
2. **Page Refresh**: Reload page and confirm session restoration
3. **Token Refresh**: Manually refresh tokens
4. **Sign Out**: Verify all tokens and data are cleared

```typescript
import { SessionTest } from '@/components/auth/SessionTest';

// Add to a protected route for testing
<SessionTest />
```

## Migration Notes

### From localStorage to AuthContext

Old pattern:
```typescript
const token = localStorage.getItem('auth_session');
```

New pattern:
```typescript
const { accessToken } = useAuth();
```

### Query Hooks

All query hooks now require the AuthContext and automatically handle token availability:

```typescript
// Queries are automatically disabled when no access token is available
const { data, loading, error } = useListingsQuery(params);
```

## Best Practices

1. **Always check token availability** before making API calls
2. **Handle token refresh failures** gracefully
3. **Use the SessionTest component** during development
4. **Monitor cookie settings** in production (secure, httpOnly, SameSite)
5. **Implement proper error handling** for authentication failures

## Security Considerations

1. **Cookie Security**: Ensure cookies are served over HTTPS in production
2. **Token Expiration**: Configure appropriate token lifespans
3. **Error Handling**: Don't expose sensitive information in error messages
4. **Logout**: Ensure complete cleanup of all authentication data
5. **CORS**: Configure proper CORS settings for cookie handling

## Troubleshooting

### Common Issues

1. **Session not restored**: Check if refresh token cookie exists and is valid
2. **API calls failing**: Verify access token is available in AuthContext
3. **Infinite loading**: Check for proper error handling in token refresh
4. **Cookie not set**: Verify cookie settings and HTTPS in production

### Debug Tools

Use the SessionTest component to inspect:
- Authentication state
- Token presence
- User data loading
- Error conditions

## Future Enhancements

1. **Automatic token refresh** before expiration
2. **Background token refresh** for long-running sessions
3. **Multiple workspace support** with token scoping
4. **Enhanced error recovery** mechanisms
5. **Audit logging** for authentication events
