# Team Invitations Feature

## Overview

The team invitations feature allows organization administrators to view and manage pending invitations sent to potential team members.

## Implementation

### API Integration

- **Endpoint**: `GET /v1/organizations/current/invitations`
- **Query Parameters**: 
  - `email` (optional): Filter invitations by email address
- **Response**: Array of invitation objects with the following structure:
  ```typescript
  interface Invitation {
    id: string;
    organizationId: string;
    email: string;
    role: string;
    status: string;
    inviterId: string;
    expiresAt: string;
    createdAt?: string;
  }
  ```

### Components Added

1. **InvitationsList** (`src/components/team/InvitationsList.tsx`)
   - Displays pending invitations in a card format
   - Shows invitation details: email, role, status, creation date, expiration date
   - Handles loading and error states
   - Filters to show only pending invitations

2. **Updated TeamManagement** (`src/components/team/TeamManagement.tsx`)
   - Added new "Invitations" tab alongside Overview and Members
   - Integrated InvitationsList component

3. **Updated TeamOverview** (`src/components/team/TeamOverview.tsx`)
   - Added pending invitations count to team statistics
   - Shows invitation count only when there are pending invitations

### Hooks Added

1. **useOrganizationInvitationsQuery** (`src/hooks/useOrganizationApi.ts`)
   - React Query hook for fetching organization invitations
   - Supports optional email filtering
   - Includes proper error handling and retry logic
   - 2-minute stale time for caching

### API Client Updates

1. **getOrganizationInvitations** (`src/lib/api-client.ts`)
   - New method to fetch invitations from the API
   - Supports optional email query parameter

2. **API_ENDPOINTS** (`src/lib/api-config.ts`)
   - Added `INVITATIONS: '/v1/organizations/current/invitations'` endpoint

## Features

### Current Features
- ✅ View all pending invitations
- ✅ Display invitation details (email, role, status, dates)
- ✅ Show expired invitation status
- ✅ Integration with team overview statistics
- ✅ Proper loading and error states
- ✅ Resend invitation functionality (uses existing invite endpoint with `resend: true`)

### Resend Functionality
- **Endpoint**: Uses the existing `POST /v1/organizations/current/members` with `resend: true`
- **UI**: Shows "Resend" button for pending and expired invitations
- **Loading State**: Button shows spinner and "Sending..." text during request
- **Error Handling**: Uses existing mutation error handling with toast notifications
- **Success**: Automatically refreshes invitation list after successful resend

### Future Enhancements
- ⏳ Cancel/revoke invitation functionality (requires API endpoint)
- ⏳ Bulk invitation management
- ⏳ Invitation history and audit trail

## Usage

### Viewing Invitations

1. Navigate to the Team page
2. Click on the "Invitations" tab
3. View all pending invitations with their details

### Permissions

- Only users with `admin` or `owner` roles can view invitations
- The invitations list is automatically filtered based on user permissions

## Technical Notes

- The component uses React Query for data fetching and caching
- Invitations are automatically refreshed when new invitations are sent
- The UI gracefully handles empty states and errors
- Date formatting uses `date-fns` for consistent display
- Status badges use different colors based on invitation state