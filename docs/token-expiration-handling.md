# Authentication Error Handling

## Overview

This document describes the comprehensive authentication error handling system implemented in the broker-portfolio-forge application. The system handles two main scenarios:

1. **Token Expiration**: When a user's session expires or authentication tokens become invalid, the system automatically navigates the user to the homepage and displays a toast notification.
2. **Unauthorized Access**: When a user tries to access a resource they don't have permission for (403 Forbidden), the system redirects them to a dedicated unauthorized page.

## Implementation

### Components

1. **Global Authentication Error Handler** (`src/lib/token-expiration-handler.ts`)
   - Provides global functions for handling token expiration and unauthorized access
   - Uses a singleton pattern to avoid React hook dependencies
   - Handles navigation and toast display for both scenarios

2. **Token Expiration Setup** (`src/components/TokenExpirationSetup.tsx`)
   - Initializes the global handlers with React Router navigation and toast functions
   - Mounts inside BrowserRouter to have access to navigation context

3. **useTokenExpiration Hook** (`src/hooks/useTokenExpiration.tsx`)
   - React hook version for components that need direct access to authentication error handling
   - Uses React Router and toast hooks internally
   - Provides both token expiration and unauthorized access handling

4. **Unauthorized Page** (`src/pages/Unauthorized.tsx`)
   - Dedicated page for access denied scenarios
   - Provides clear messaging and navigation options for users
   - Accessible at `/unauthorized` route

### Integration Points

The token expiration handling is integrated at multiple levels:

1. **AuthContext** (`src/contexts/AuthContext.tsx`)
   - Handles session refresh failures
   - Detects SESSION_EXPIRED errors

2. **API Hooks** (`src/hooks/useApi.tsx`)
   - Intercepts 401 errors and token expiration messages
   - Prevents normal error handling when token expires

3. **React Query** (`src/lib/query-client.ts`)
   - Global error handler for all queries
   - Catches token expiration in background queries

## Error Scenarios

### Token Expiration (401 Unauthorized)

This occurs when:
- User's session has expired
- Access token is invalid or malformed
- Refresh token has expired
- Authentication service is unavailable

**Behavior**: Redirects to homepage (`/`) with "Session Expired" toast

### Unauthorized Access (403 Forbidden)

This occurs when:
- User doesn't have required permissions for a resource
- User's role doesn't allow access to a specific feature
- Workspace-level restrictions apply
- Resource-specific access controls deny access

**Behavior**: Redirects to unauthorized page (`/unauthorized`) with "Access Denied" toast

## How It Works

### Detection

Token expiration is detected through multiple criteria:

```typescript
// AuthErrorCode.SESSION_EXPIRED
error?.code === AuthErrorCode.SESSION_EXPIRED

// HTTP 401 status
error?.status === 401

// Common error messages
message.includes('session expired') || 
message.includes('token expired') ||
message.includes('unauthorized') ||
message.includes('invalid token')
```

### Handling Flow

1. **Error Detection**: Any API call or authentication operation detects token expiration
2. **Global Handler**: Calls `handleTokenExpiration()` function
3. **Toast Display**: Shows "Session Expired" toast with destructive variant for 3 seconds
4. **Navigation**: Navigates to homepage (`/`) with `replace: true` after 100ms delay
5. **Cleanup**: Clears authentication state and tokens

### Toast Configuration

```typescript
toast({
  title: "Session Expired",
  description: "Your session has expired. Please sign in again.",
  variant: "destructive",
  duration: 3000, // 3 seconds
});
```

### Navigation

```typescript
setTimeout(() => {
  navigate('/', { replace: true });
}, 100); // Brief delay to ensure toast is visible
```

## Usage Examples

### Automatic Handling

Most of the time, token expiration is handled automatically:

```typescript
// This will automatically handle token expiration
const { data, loading, error } = useListingsQuery();

// This will also handle token expiration  
const { execute } = useApi(apiClient.createListing);
```

### Manual Handling

For custom error handling scenarios:

```typescript
import { handleTokenExpiration, isTokenExpiredError } from '@/lib/token-expiration-handler';

try {
  await apiCall();
} catch (error) {
  if (isTokenExpiredError(error)) {
    handleTokenExpiration(error);
    return; // Don't continue with normal error handling
  }
  // Handle other errors normally
}
```

## Setup

The system is automatically set up in `App.tsx`:

```typescript
<BrowserRouter>
  <TokenExpirationSetup />
  <AppRoutes />
</BrowserRouter>
```

This ensures the global handlers are available throughout the application.

## Benefits

1. **Consistent UX**: All token expiration scenarios show the same user-friendly message
2. **Automatic Navigation**: Users are automatically redirected to a safe page
3. **Clean Separation**: Token expiration logic is separated from normal error handling
4. **Global Coverage**: Works for all API calls, queries, and authentication operations
5. **Non-blocking**: Doesn't interfere with normal application flow

## Security Considerations

1. **Immediate Cleanup**: All authentication data is cleared immediately upon token expiration
2. **Safe Navigation**: Users are redirected to a public page that doesn't require authentication
3. **Clear Communication**: Users are informed about what happened and what to do next
4. **Replace Navigation**: Uses `replace: true` to prevent back navigation to authenticated pages

## Troubleshooting

### Toast Not Showing

- Verify `TokenExpirationSetup` is mounted inside `BrowserRouter`
- Check that toast system is properly initialized in `App.tsx`
- Ensure global handlers are set before first API call

### Navigation Not Working

- Confirm `TokenExpirationSetup` has access to `useNavigate`
- Verify it's inside `BrowserRouter` context
- Check browser console for navigation errors

### Multiple Toasts

- The system calls `dismiss()` before showing new toast to prevent duplicates
- If still occurring, check for multiple error handling paths

## Future Enhancements

1. **Customizable Messages**: Allow different messages for different expiration scenarios
2. **Retry Logic**: Attempt automatic token refresh before showing expiration
3. **Background Refresh**: Proactively refresh tokens before expiration
4. **Analytics**: Track token expiration events for monitoring 