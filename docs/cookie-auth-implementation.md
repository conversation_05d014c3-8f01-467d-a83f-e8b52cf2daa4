# Cookie-Based Authentication Implementation

## Overview

Updated the frontend authentication system to work with the new backend cookie-based refresh token implementation. This provides enhanced security by storing refresh tokens in httpOnly cookies that cannot be accessed by JavaScript.

## Key Changes

### 1. AuthContext Updates (`src/contexts/AuthContext.tsx`)

**Removed Client-Side Token Management:**
- Removed `storeRefreshToken()`, `getRefreshToken()`, and `clearRefreshToken()` functions
- No longer managing refresh tokens on the client side
- Backend now handles all refresh token storage via httpOnly cookies

**Updated Authentication Flow:**
- Sign up/in: Only stores access token in memory, refresh token handled by backend
- Sign out: Calls API without refresh token (backend clears cookie)
- Refresh: Calls API without request body (backend reads from cookie)
- Session initialization: Attempts refresh on app load (backend validates cookie)

### 2. API Client Updates (`src/lib/generated-api-client.ts`)

**Added Cookie Support:**
- Added `credentials: 'include'` to all fetch requests
- Ensures cookies are sent with every API request
- Required for httpOnly cookie authentication to work

**Updated Method Signatures:**
- `refreshToken()` now accepts empty object (no refresh token in body)
- `signOut()` now accepts empty object (no refresh token needed)

### 3. Security Improvements

**XSS Protection:**
- Refresh tokens stored in httpOnly cookies cannot be accessed by malicious scripts
- Access tokens in memory are cleared on page refresh

**CSRF Protection:**
- Backend sets SameSite=Strict on cookies
- Prevents cross-site request forgery attacks

**Transport Security:**
- Backend sets secure=true in production (HTTPS only)
- Automatic cookie management by browser

## Usage

### Authentication Flow

```typescript
// Sign in - cookies handled automatically
const { signIn } = useAuth();
await signIn(email, password);

// Refresh session - no token management needed
const { refreshSession } = useAuth();
await refreshSession();

// Sign out - backend clears cookies
const { signOut } = useAuth();
await signOut();
```

### Testing

Use the `CookieAuthTest` component to verify the implementation:

```typescript
import { CookieAuthTest } from '@/components/auth/CookieAuthTest';

// Add to your development routes
<CookieAuthTest />
```

## Backend Requirements

The backend must implement:

1. **Cookie Setting:** Set httpOnly, secure, SameSite=Strict cookies
2. **Cookie Reading:** Read refresh tokens from cookies in refresh endpoint
3. **Cookie Clearing:** Clear cookies on sign out and invalid refresh
4. **CORS Configuration:** Allow credentials and proper origins

## Migration Notes

### What Changed
- ✅ Removed client-side refresh token storage
- ✅ Added `credentials: 'include'` to all API requests
- ✅ Updated auth flow to rely on backend cookie management
- ✅ Simplified token refresh logic

### What Stayed the Same
- Access tokens still stored in React state (memory)
- User data still stored in React state
- Authentication context API unchanged for components
- Error handling and loading states unchanged

## Security Benefits

1. **XSS Mitigation:** httpOnly cookies prevent script access to refresh tokens
2. **CSRF Protection:** SameSite=Strict prevents cross-site attacks
3. **Transport Security:** Secure flag ensures HTTPS-only transmission
4. **Reduced Attack Surface:** No client-side token storage to compromise
5. **Automatic Management:** Browser handles cookie lifecycle

## Troubleshooting

### Common Issues

**Cookies Not Being Sent:**
- Ensure `credentials: 'include'` is set on all requests
- Check CORS configuration allows credentials
- Verify same-origin or proper CORS setup

**Session Not Restoring:**
- Check if refresh endpoint is working
- Verify cookies are being set by backend
- Check browser developer tools for cookie presence

**CORS Errors:**
- Backend must set `Access-Control-Allow-Credentials: true`
- Backend must specify exact origins, not wildcards with credentials
- Ensure preflight requests are handled properly

### Development vs Production

**Development (HTTP):**
- Cookies set with `secure: false`
- Works on localhost without HTTPS

**Production (HTTPS):**
- Cookies set with `secure: true`
- Requires HTTPS for cookie transmission
- More restrictive security settings