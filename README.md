# Frontend Application

This is the frontend application for the broker portfolio platform, integrated into a monorepo structure while maintaining its own separate git repository.

## Monorepo Integration

This frontend application is part of a larger monorepo but maintains its own git repository for independent development and deployment. The application has been configured to:

- Use shared TypeScript types from the `shared-types` package
- Work with the monorepo's unified development scripts
- Connect to the backend services via Docker Compose
- Maintain type-safe Supabase client integration

## Development

### Within the Monorepo

From the monorepo root, you can:

```sh
# Start both frontend and backend
bun run dev

# Start only the frontend
bun run dev:frontend

# Build the frontend
bun run build:frontend

# Lint the frontend
bun run lint:frontend
```

### Standalone Development

You can also work on this frontend independently:

```sh
# Install dependencies
bun install

# Start development server
bun run dev

# Build for production
bun run build
```

## Environment Configuration

Copy `.env.example` to `.env` and configure:

```env
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_API_URL=http://localhost:3000
```

## Shared Types Integration

This application uses shared TypeScript types from the monorepo's `shared-types` package:

```typescript
import { Database } from 'shared-types/database'
import { supabase } from '@/lib/supabase'

// Fully typed Supabase client
const { data, error } = await supabase
  .from('your_table')
  .select('*')
```

## Git Workflow

This frontend maintains its own git repository. You can:

- Commit and push changes to the frontend repository independently
- Pull updates from the original frontend repository
- Maintain separate branches and releases for the frontend

## Project info

**URL**: https://lovable.dev/projects/15a876d3-8f0d-4e15-a589-444086fdc75f

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/15a876d3-8f0d-4e15-a589-444086fdc75f) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/15a876d3-8f0d-4e15-a589-444086fdc75f) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
